/*
 * Copyright (c) 2023 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.text.TextUtils;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import com.sky.xposed.annotations.APlugin;
import com.sky.xposed.common.util.Alog;
import com.sky.xposed.core.interfaces.XCoreManager;
import com.sky.xposed.rimet.XConstant;
import com.sky.xposed.rimet.plugin.base.LazyBaseDingPlugin;
import com.sky.xposed.rimet.plugin.base.LazyLoadable;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * 智能虚拟定位插件
 * 根据时间自动切换虚拟定位状态
 */
@APlugin
public class SmartLocationPlugin extends LazyBaseDingPlugin {

    private static final String TAG = "SmartLocationPlugin";
    private static final String DEFAULT_WORK_START_TIME = "07:00";
    private static final String DEFAULT_WORK_END_TIME = "18:00";
    
    // 将timeFormat改为静态常量
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm", Locale.getDefault());
    
    // 缓存系统
    private int mCurrentMinutes = -1;
    private long mLastTimeCheck = 0;
    private static final long TIME_CHECK_INTERVAL = 60000; // 60秒检查一次（减少频率）
    
    // 缓存上次计算结果
    private boolean mLastVirtualLocationEnabled = false;
    private long mLastCalculationTime = 0;
    private static final long CALCULATION_CACHE_INTERVAL = 30000; // 30秒内不重复计算（增加缓存时间）
    
    // 动态检查频率控制
    private volatile boolean mIsAppInForeground = true;
    private volatile boolean mIsScreenOn = true;
    private long mCurrentCheckInterval = 60; // 当前检查间隔（秒）
    private static final long FOREGROUND_CHECK_INTERVAL = 60; // 前台检查间隔（秒）
    private static final long BACKGROUND_CHECK_INTERVAL = 300; // 后台检查间隔（5分钟）
    private static final long SCREEN_OFF_CHECK_INTERVAL = 600; // 息屏检查间隔（10分钟）
    
    // 智能休眠机制
    private volatile boolean mIsInSleepMode = false;
    private long mLastActiveTime = System.currentTimeMillis();
    private static final long SLEEP_THRESHOLD = 30 * 60 * 1000; // 30分钟无活动后进入休眠
    private static final long DEEP_SLEEP_CHECK_INTERVAL = 1800; // 深度休眠检查间隔（30分钟）
    
    // 性能监控
    private long mTotalCheckCount = 0;
    private long mTotalCheckTime = 0;
    private long mLastPerformanceReport = System.currentTimeMillis();
    private static final long PERFORMANCE_REPORT_INTERVAL = 10 * 60 * 1000; // 10分钟报告一次性能
    
    // 单例实现
    private static SmartLocationPlugin sInstance;
    
    // 异步处理相关
    private HandlerThread mBackgroundThread;
    private Handler mBackgroundHandler;
    private ScheduledExecutorService mScheduledExecutor;
    private volatile boolean mIsDestroyed = false;
    
    // 生命周期监听
    private Application.ActivityLifecycleCallbacks mLifecycleCallbacks;
    private BroadcastReceiver mScreenStateReceiver;
    private int mActiveActivityCount = 0;
    
    public SmartLocationPlugin(XCoreManager coreManager) {
        super(coreManager);
        sInstance = this;
        initBackgroundThread();
    }
    
    /**
     * 初始化后台线程
     */
    private void initBackgroundThread() {
        mBackgroundThread = new HandlerThread("SmartLocationPlugin-Background");
        mBackgroundThread.start();
        mBackgroundHandler = new Handler(mBackgroundThread.getLooper());
        mScheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "SmartLocationPlugin-Scheduler");
            t.setDaemon(true);
            return t;
        });
    }
    
    /**
     * 获取SmartLocationPlugin实例，供LocationPlugin调用
     */
    public static SmartLocationPlugin getInstance() {
        return sInstance;
    }

    @Override
    public Priority getLoadPriority() {
        // SmartLocationPlugin设置为低优先级，可以延迟加载
        return Priority.LOW;
    }

    @Override
    public boolean canLazyLoad() {
        // 允许延迟加载，因为智能定位不是关键功能
        return true;
    }

    @Override
    protected void doPreLazyLoad() {
        // 预加载时进行轻量级初始化
        Alog.d(TAG, "SmartLocationPlugin 预加载 - 初始化时间格式化器");
        // 时间格式化器已经是静态常量，无需额外初始化
    }

    @Override
    protected void doLazyLoadComplete() {
        // 延迟加载完成后的处理
        Alog.d(TAG, "SmartLocationPlugin 延迟加载完成 - 开始定期检查");
        // 注册生命周期监听
        registerLifecycleCallbacks();
        // 注册屏幕状态监听
        registerScreenStateReceiver();
        // 启动定期检查任务
        startPeriodicCheck();
    }

    @Override
    public void hook() {
        try {
            Alog.d(TAG, "初始化SmartLocationPlugin...");
            checkAndUpdateLocationSetting();
        } catch (Throwable t) {
            Alog.e(TAG, "Hook初始化异常", t);
        }
    }

    /**
     * 启动定期检查任务（异步执行，动态调整频率）
     */
    private void startPeriodicCheck() {
        if (mIsDestroyed) return;
        
        // 启动动态调整的定期检查
        scheduleNextCheck(5); // 初始延迟5秒
        
        Alog.d(TAG, "动态频率异步定期检查任务已启动");
    }
    
    /**
     * 调度下一次检查（根据应用状态动态调整间隔）
     */
    private void scheduleNextCheck(long initialDelay) {
        if (mIsDestroyed) return;
        
        // 根据应用状态确定检查间隔
        updateCheckInterval();
        
        mScheduledExecutor.schedule(() -> {
            if (mIsDestroyed) return;
            
            try {
                // 在后台线程执行时间检查
                checkAndUpdateLocationSettingAsync();
                
                // 调度下一次检查
                scheduleNextCheck(mCurrentCheckInterval);
            } catch (Throwable e) {
                Alog.e(TAG, "定期检查异常", e);
                // 出错时使用默认间隔重新调度
                scheduleNextCheck(FOREGROUND_CHECK_INTERVAL);
            }
        }, initialDelay, TimeUnit.SECONDS);
    }
    
    /**
     * 根据应用状态更新检查间隔（包含智能休眠）
     */
    private void updateCheckInterval() {
        // 检查是否应该进入休眠模式
        checkSleepMode();
        
        long newInterval;
        
        if (mIsInSleepMode) {
            // 休眠模式使用最长间隔
            newInterval = DEEP_SLEEP_CHECK_INTERVAL;
        } else if (!mIsScreenOn) {
            // 息屏时使用长间隔
            newInterval = SCREEN_OFF_CHECK_INTERVAL;
        } else if (!mIsAppInForeground) {
            // 后台时使用中等间隔
            newInterval = BACKGROUND_CHECK_INTERVAL;
        } else {
            // 前台时使用正常间隔
            newInterval = FOREGROUND_CHECK_INTERVAL;
        }
        
        if (newInterval != mCurrentCheckInterval) {
            Alog.d(TAG, "检查间隔调整: " + mCurrentCheckInterval + "s -> " + newInterval + "s" +
                    " (前台:" + mIsAppInForeground + ", 亮屏:" + mIsScreenOn + ", 休眠:" + mIsInSleepMode + ")");
            mCurrentCheckInterval = newInterval;
        }
    }
    
    /**
     * 检查是否应该进入休眠模式
     */
    private void checkSleepMode() {
        long now = System.currentTimeMillis();
        boolean shouldSleep = !mIsAppInForeground && !mIsScreenOn && 
                             (now - mLastActiveTime) > SLEEP_THRESHOLD;
        
        if (shouldSleep != mIsInSleepMode) {
            mIsInSleepMode = shouldSleep;
            if (mIsInSleepMode) {
                Alog.d(TAG, "进入智能休眠模式");
            } else {
                Alog.d(TAG, "退出智能休眠模式");
            }
        }
    }
    
    /**
     * 更新最后活动时间
     */
    private void updateLastActiveTime() {
        mLastActiveTime = System.currentTimeMillis();
        if (mIsInSleepMode) {
            mIsInSleepMode = false;
            Alog.d(TAG, "用户活动检测，退出休眠模式");
        }
    }
    
    /**
     * 异步版本的时间检查和位置设置更新
     */
    private void checkAndUpdateLocationSettingAsync() {
        // 在后台线程执行
        mBackgroundHandler.post(() -> {
            if (mIsDestroyed) return;
            
            try {
                boolean result = checkAndUpdateLocationSetting();
                Alog.v(TAG, "异步检查完成，虚拟定位状态: " + result);
            } catch (Throwable e) {
                Alog.e(TAG, "异步检查异常", e);
            }
        });
    }
    
    /**
     * 优化的时间比较函数 - 使用整数分钟比较替代Calendar对象
     */
    public boolean checkAndUpdateLocationSetting() {
        long startTime = System.currentTimeMillis();
        try {
            if (!isEnable(XConstant.Key.ENABLE_SMART_LOCATION)) {
                return getPBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, false);
            }
            
            // 使用缓存减少重复计算
            long now = System.currentTimeMillis();
            if (now - mLastCalculationTime < CALCULATION_CACHE_INTERVAL) {
                return mLastVirtualLocationEnabled;
            }
            
            // 批量获取智能定位相关配置（使用缓存优化）
            String workStartTime, workEndTime;
            if (getPreferencesCache() != null) {
                java.util.Map<String, Object> configs = getPreferencesCache().getBatch(
                    XConstant.Key.SMART_LOCATION_WORK_START_TIME,
                    XConstant.Key.SMART_LOCATION_WORK_END_TIME,
                    XConstant.Key.ENABLE_VIRTUAL_LOCATION
                );
                workStartTime = (String) configs.getOrDefault(XConstant.Key.SMART_LOCATION_WORK_START_TIME, DEFAULT_WORK_START_TIME);
                workEndTime = (String) configs.getOrDefault(XConstant.Key.SMART_LOCATION_WORK_END_TIME, DEFAULT_WORK_END_TIME);
            } else {
                // 回退到原有方式
                workStartTime = getPString(XConstant.Key.SMART_LOCATION_WORK_START_TIME, DEFAULT_WORK_START_TIME);
                workEndTime = getPString(XConstant.Key.SMART_LOCATION_WORK_END_TIME, DEFAULT_WORK_END_TIME);
            }
            
            // 检查时间格式是否有效
            if (!isValidTimeFormat(workStartTime) || !isValidTimeFormat(workEndTime)) {
                mLastVirtualLocationEnabled = getPBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, false);
                mLastCalculationTime = now;
                return mLastVirtualLocationEnabled;
            }
            
            // 转换为分钟数进行比较
            int currentMinutes = getCurrentTimeMinutes();
            int startMinutes = timeToMinutes(workStartTime);
            int endMinutes = timeToMinutes(workEndTime);
            
            // 判断当前时间是否在工作时间外
            boolean shouldEnableVirtual = currentMinutes < startMinutes || currentMinutes >= endMinutes;
            
            // 更新虚拟定位状态
            boolean currentSetting = getPBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, false);
            if (currentSetting != shouldEnableVirtual) {
                Alog.d(TAG, "更新虚拟定位状态: " + (shouldEnableVirtual ? "启用" : "禁用") + 
                        " (当前时间: " + currentMinutes + "分钟, 工作时间: " + 
                        startMinutes + "-" + endMinutes + "分钟)");
                putPBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, shouldEnableVirtual);
            }
            
            // 更新缓存
            mLastVirtualLocationEnabled = shouldEnableVirtual;
            mLastCalculationTime = now;
            
            return shouldEnableVirtual;
        } catch (Throwable t) {
            Alog.e(TAG, "检查位置设置异常", t);
            return getPBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, false);
        } finally {
            // 性能监控
            recordPerformanceMetrics(startTime);
        }
    }
    
    /**
     * 记录性能指标
     */
    private void recordPerformanceMetrics(long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        mTotalCheckCount++;
        mTotalCheckTime += duration;
        
        // 定期报告性能数据
        long now = System.currentTimeMillis();
        if (now - mLastPerformanceReport > PERFORMANCE_REPORT_INTERVAL) {
            double avgTime = mTotalCheckCount > 0 ? (double) mTotalCheckTime / mTotalCheckCount : 0;
            Alog.i(TAG, String.format("性能报告 - 总检查次数: %d, 平均耗时: %.2fms, 当前间隔: %ds, 休眠状态: %s",
                    mTotalCheckCount, avgTime, mCurrentCheckInterval, mIsInSleepMode ? "是" : "否"));
            
            // 重置计数器
            mTotalCheckCount = 0;
            mTotalCheckTime = 0;
            mLastPerformanceReport = now;
        }
    }
    
    /**
     * 时间字符串转换为分钟数
     */
    private int timeToMinutes(String timeStr) {
        try {
            String[] parts = timeStr.split(":");
            if (parts.length == 2) {
                int hours = Integer.parseInt(parts[0]);
                int minutes = Integer.parseInt(parts[1]);
                return hours * 60 + minutes;
            }
        } catch (Exception e) {
            Alog.e(TAG, "时间转换异常: " + timeStr, e);
        }
        return -1;
    }
    
    /**
     * 获取当前时间的分钟数（带缓存）
     */
    private int getCurrentTimeMinutes() {
        long now = System.currentTimeMillis();
        if (mCurrentMinutes == -1 || now - mLastTimeCheck > TIME_CHECK_INTERVAL) {
            Calendar cal = Calendar.getInstance();
            mCurrentMinutes = cal.get(Calendar.HOUR_OF_DAY) * 60 + cal.get(Calendar.MINUTE);
            mLastTimeCheck = now;
        }
        return mCurrentMinutes;
    }
    
    /**
     * 验证时间格式
     */
    private boolean isValidTimeFormat(String timeStr) {
        if (TextUtils.isEmpty(timeStr)) return false;
        
        return timeStr.matches("^([01]?[0-9]|2[0-3]):([0-5][0-9])$");
    }
    
    /**
     * 注册应用生命周期回调（优化版本）
     */
    private void registerLifecycleCallbacks() {
        try {
            Application app = (Application) getLoadPackage().getContext();
            
            mLifecycleCallbacks = new Application.ActivityLifecycleCallbacks() {
                @Override
                public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                    // 不需要处理
                }

                @Override
                public void onActivityStarted(Activity activity) {
                    mActiveActivityCount++;
                    if (mActiveActivityCount == 1) {
                        // 应用进入前台
                        onAppForeground();
                    }
                }

                @Override
                public void onActivityResumed(Activity activity) {
                    // 不需要处理
                }

                @Override
                public void onActivityPaused(Activity activity) {
                    // 不需要处理
                }

                @Override
                public void onActivityStopped(Activity activity) {
                    mActiveActivityCount--;
                    if (mActiveActivityCount <= 0) {
                        mActiveActivityCount = 0;
                        // 应用进入后台
                        onAppBackground();
                    }
                }

                @Override
                public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
                    // 不需要处理
                }

                @Override
                public void onActivityDestroyed(Activity activity) {
                    // 不需要处理
                }
            };
            
            app.registerActivityLifecycleCallbacks(mLifecycleCallbacks);
            Alog.d(TAG, "生命周期回调已注册");
        } catch (Throwable e) {
            Alog.e(TAG, "注册生命周期回调失败", e);
        }
    }
    
    /**
     * 注册屏幕状态监听
     */
    private void registerScreenStateReceiver() {
        try {
            Context context = getLoadPackage().getContext();
            
            mScreenStateReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    if (Intent.ACTION_SCREEN_ON.equals(action)) {
                        onScreenOn();
                    } else if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                        onScreenOff();
                    }
                }
            };
            
            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_SCREEN_ON);
            filter.addAction(Intent.ACTION_SCREEN_OFF);
            
            context.registerReceiver(mScreenStateReceiver, filter);
            Alog.d(TAG, "屏幕状态监听已注册");
        } catch (Throwable e) {
            Alog.e(TAG, "注册屏幕状态监听失败", e);
        }
    }
    
    /**
     * 应用进入前台
     */
    private void onAppForeground() {
        mIsAppInForeground = true;
        updateLastActiveTime(); // 更新活动时间
        Alog.d(TAG, "应用进入前台");
        // 立即检查一次位置设置
        checkAndUpdateLocationSettingAsync();
    }
    
    /**
     * 应用进入后台
     */
    private void onAppBackground() {
        mIsAppInForeground = false;
        Alog.d(TAG, "应用进入后台");
    }
    
    /**
     * 屏幕点亮
     */
    private void onScreenOn() {
        mIsScreenOn = true;
        updateLastActiveTime(); // 更新活动时间
        Alog.d(TAG, "屏幕点亮");
        if (mIsAppInForeground) {
            // 如果应用在前台且屏幕点亮，立即检查一次
            checkAndUpdateLocationSettingAsync();
        }
    }
    
    /**
     * 屏幕熄灭
     */
    private void onScreenOff() {
        mIsScreenOn = false;
        Alog.d(TAG, "屏幕熄灭");
    }
    
    /**
     * 插件释放资源
     */
    @Override
    public void release() {
        mIsDestroyed = true;
        
        // 注销生命周期回调
        if (mLifecycleCallbacks != null) {
            try {
                Application app = (Application) getLoadPackage().getContext();
                app.unregisterActivityLifecycleCallbacks(mLifecycleCallbacks);
                mLifecycleCallbacks = null;
                Alog.d(TAG, "生命周期回调已注销");
            } catch (Throwable e) {
                Alog.e(TAG, "注销生命周期回调失败", e);
            }
        }
        
        // 注销屏幕状态监听
        if (mScreenStateReceiver != null) {
            try {
                getLoadPackage().getContext().unregisterReceiver(mScreenStateReceiver);
                mScreenStateReceiver = null;
                Alog.d(TAG, "屏幕状态监听已注销");
            } catch (Throwable e) {
                Alog.e(TAG, "注销屏幕状态监听失败", e);
            }
        }
        
        // 关闭线程池
        if (mScheduledExecutor != null && !mScheduledExecutor.isShutdown()) {
            mScheduledExecutor.shutdown();
            try {
                if (!mScheduledExecutor.awaitTermination(2, TimeUnit.SECONDS)) {
                    mScheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                mScheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 关闭后台线程
        if (mBackgroundThread != null) {
            mBackgroundThread.quitSafely();
            try {
                mBackgroundThread.join(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            mBackgroundThread = null;
            mBackgroundHandler = null;
        }
        
        sInstance = null;
        super.release();
    }
} 