---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
# Task List Management
> *中文说明: 任务列表管理*

Guidelines for creating and managing task lists in markdown files to track project progress
> *中文说明: 在 markdown 文件中创建和管理任务列表以跟踪项目进度的指南*

## Task List Creation
> *中文说明: 任务列表创建*

1. Create task lists in a markdown file (in the project root):
   - Use `TASKS.md` or a descriptive name relevant to the feature (e.g., `ASSISTANT_CHAT.md`)
   - Include a clear title and description of the feature being implemented

> *中文说明:*
> *1. 在 markdown 文件中创建任务列表（位于项目根目录）：*
>    *- 使用 `TASKS.md` 或者与功能相关的描述性名称（例如，`ASSISTANT_CHAT.md`）*
>    *- 包含清晰的标题和正在实现的功能描述*

2. Structure the file with these sections:
   ```markdown
   # Feature Name Implementation
   
   Brief description of the feature and its purpose.
   
   ## Completed Tasks
   
   - [x] Task 1 that has been completed
   - [x] Task 2 that has been completed
   
   ## In Progress Tasks
   
   - [ ] Task 3 currently being worked on
   - [ ] Task 4 to be completed soon
   
   ## Future Tasks
   
   - [ ] Task 5 planned for future implementation
   - [ ] Task 6 planned for future implementation
   
   ## Implementation Plan
   
   Detailed description of how the feature will be implemented.
   
   ### Relevant Files
   
   - path/to/file1.ts - Description of purpose
   - path/to/file2.ts - Description of purpose
   ```

> *中文说明:*
> *2. 使用以下部分构建文件：*
>    ```markdown
>    # 功能名称实现
>    
>    该功能及其目的的简要描述。
>    
>    ## 已完成任务
>    
>    - [x] 已完成的任务 1
>    - [x] 已完成的任务 2
>    
>    ## 进行中任务
>    
>    - [ ] 当前正在进行的任务 3
>    - [ ] 即将完成的任务 4
>    
>    ## 未来任务
>    
>    - [ ] 计划未来实现的任务 5
>    - [ ] 计划未来实现的任务 6
>    
>    ## 实现计划
>    
>    关于如何实现该功能的详细描述。
>    
>    ### 相关文件
>    
>    - path/to/file1.ts - 用途描述
>    - path/to/file2.ts - 用途描述
>    ```

## Task List Maintenance
> *中文说明: 任务列表维护*

1. Update the task list as you progress:
   - Mark tasks as completed by changing `[ ]` to `[x]`
   - Add new tasks as they are identified
   - Move tasks between sections as appropriate

> *中文说明:* 
> *1. 随着进度更新任务列表：*
>    *- 通过将 `[ ]` 更改为 `[x]` 标记任务为已完成*
>    *- 添加新识别的任务*
>    *- 在各部分之间适当移动任务*

2. Keep "Relevant Files" section updated with:
   - File paths that have been created or modified
   - Brief descriptions of each file's purpose
   - Status indicators (e.g., ✅) for completed components

> *中文说明:*
> *2. 保持"相关文件"部分更新：*
>    *- 已创建或修改的文件路径*
>    *- 每个文件用途的简要描述*
>    *- 已完成组件的状态指示器（例如，✅）*

3. Add implementation details:
   - Architecture decisions
   - Data flow descriptions
   - Technical components needed
   - Environment configuration

> *中文说明:*
> *3. 添加实现细节：*
>    *- 架构决策*
>    *- 数据流描述*
>    *- 所需技术组件*
>    *- 环境配置*

## AI Instructions
> *中文说明: AI 指令*

When working with task lists, the AI should:

> *中文说明: 在处理任务列表时，AI 应该：*

1. Regularly update the task list file after implementing significant components
2. Mark completed tasks with [x] when finished
3. Add new tasks discovered during implementation
4. Maintain the "Relevant Files" section with accurate file paths and descriptions
5. Document implementation details, especially for complex features
6. When implementing tasks one by one, first check which task to implement next
7. After implementing a task, update the file to reflect progress

> *中文说明:*
> *1. 实现重要组件后定期更新任务列表文件*
> *2. 完成后使用 [x] 标记已完成的任务*
> *3. 添加在实现过程中发现的新任务*
> *4. 维护"相关文件"部分，包含准确的文件路径和描述*
> *5. 记录实现细节，特别是对于复杂功能*
> *6. 当逐个实现任务时，首先检查下一步要实现的任务*
> *7. 实现任务后，更新文件以反映进度*

## Example Task Update
> *中文说明: 任务更新示例*

When updating a task from "In Progress" to "Completed":

> *中文说明: 当将任务从"进行中"更新为"已完成"时：*

```markdown
## In Progress Tasks

- [ ] Implement database schema
- [ ] Create API endpoints for data access

## Completed Tasks

- [x] Set up project structure
- [x] Configure environment variables
```

Should become:

> *中文说明: 应变为：*

```markdown
## In Progress Tasks

- [ ] Create API endpoints for data access

## Completed Tasks

- [x] Set up project structure
- [x] Configure environment variables
- [x] Implement database schema
```

Please use my three MCP services for the following matters:
> *请在以下事项中使用我的两个mcp服务*

1. mcp_server-sequentialthinking —— Used for planning each step and ensuring that we thoroughly and maximally complete this process during execution.
2. mcp_server-Context7 MCP —— During the research period, as well as before implementing any new third-party APIs, modifying project structures, or making any changes.
3. 当需要执行搜索代码、查找文件、编辑文件等文件系统的操作时，优先使用mcp_server-filesystem来进行操作

> *1. sequentialthinking —— 用于规划每一个步骤，并确保我们在执行过程中彻底而最大化地完成这一流程。2.Context7 MCP —— 在进行研究期间，以及在实施任何新的第三方API或修改项目结构或进行任何变更之前。*