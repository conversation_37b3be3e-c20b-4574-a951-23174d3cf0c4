/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data.source;

import com.sky.xposed.rimet.data.model.ConfigModel;
import com.sky.xposed.rimet.data.model.UpdateModel;
import com.sky.xposed.rimet.data.model.VersionModel;

import io.reactivex.Observable;

/**
 * Created by sky on 2019-05-27.
 */
public interface IRimetSource {

    /**
     * 检测版本更新
     * @return
     */
    Observable<UpdateModel> checkUpdate();


    /**
     * 获取支持的版本
     * @return
     */
    Observable<VersionModel> getSupportVersion();


    /**
     * 获取相应的版本版本
     * @param versionCode 版本号
     * @return
     */
    Observable<ConfigModel> getVersionConfig(String versionCode);
}
