/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data;

/**
 * Created by sky on 2019/3/12.
 */
public final class M {

    private M() {

    }

    public static final class sky {

        public static final int rimet_package_md5 = 0x00000001;
    }

    public static final class classz {

        public static final int class_rimet_LauncherApplication = 0x1f000001;

        public static final int class_dingtalkbase_multidexsupport_DDApplication = 0x1f000002;

        public static final int class_defpackage_MessageDs = 0x1f000003;

        public static final int class_plugin_webwx_ui_ExtDeviceWXLoginUI = 0x1f000004;

        public static final int class_defpackage_ServiceFactory = 0x1f000005;

        public static final int class_android_dingtalk_redpackets_idl_service_RedEnvelopPickIService = 0x1f000006;

        public static final int class_defpackage_RedPacketsRpc = 0x1f000007;

        public static final int class_defpackage_RedPacketsRpc_9 = 0x1f000008;

        public static final int class_lightapp_runtime_LightAppRuntimeReverseInterfaceImpl = 0x1f000009;

        public static final int class_android_dingtalk_redpackets_activities_FestivalRedPacketsPickActivity = 0x1f00000A;

        public static final int class_android_dingtalk_redpackets_activities_PickRedPacketsActivity = 0x1f00000B;

        public static final int class_android_user_settings_activity_NewSettingActivity = 0x1f00000C;

        public static final int class_wukong_im_base_IMDatabase = 0x1f00000D;
    }

    public static final class method {

        public static final int method_dingtalkbase_multidexsupport_DDApplication_onCreate = 0x2f000001;

        public static final int method_defpackage_MessageDs_handler = 0x2f000002;

        public static final int method_android_dingtalkim_base_model_typeValue = 0x2f000003;

        public static final int method_wukong_im_message_MessageImpl_messageContent = 0x2f000004;

        public static final int method_wukong_im_message_MessageContentImpl_contents = 0x2f000005;

        public static final int method_defpackage_RedPacketsRpc_newInstance = 0x2f000006;

        public static final int method_defpackage_ServiceFactory_getService = 0x2f000007;

        public static final int method_android_dingtalk_redpackets_idl_service_RedEnvelopPickIService_pickRedEnvelopCluster = 0x2f000008;

        public static final int method_lightapp_runtime_LightAppRuntimeReverseInterfaceImpl_initSecurityGuard = 0x2f000009;

        public static final int method_android_dingtalk_redpackets_activities_FestivalRedPacketsPickActivity_initView = 0x2f00000A;

        public static final int method_android_dingtalk_redpackets_activities_PickRedPacketsActivity_initView = 0x2f00000B;

        public static final int method_android_user_settings_activity_NewSettingActivity_onCreate = 0x2f00000C;

        public static final int method_defpackage_MessageDs_recall = 0x2f00000D;

        public static final int method_wukong_im_conversation_ConversationImpl_latestMessage = 0x2f00000E;

        public static final int method_wukong_im_base_IMDatabase_getWritableDatabase = 0x2f00000F;

        public static final int method_defpackage_MessageDs_update = 0x2f000010;

        public static final int method_wukong_im_message_MessageContentImpl_TextContentImpl_text = 0x2f000011;

        public static final int method_wukong_im_message_MessageContentImpl_TextContentImpl_setText = 0x2f000012;
    }

    public static final class field {

        public static final int field_android_dingtalkim_base_model_DingtalkMessage_msgDisplayType = 0x3f000001;

        public static final int field_wukong_im_message_MessageContentImpl_CustomMessageContentImpl_mExtension = 0x3f000002;

        public static final int field_android_dingtalkim_base_model_DingtalkMessage_mConversation = 0x3f000003;
    }

    public static final class key {

        public static final int key_sid = 0x4f000001;

        public static final int key_clusterid = 0x4f000002;
    }

    public static final class res {

        public static final int res_iv_pick = 0x5f000001;

        public static final int res_btn_pick = 0x5f000002;

        public static final int res_setting_msg_notice = 0x5f000003;
    }
}
