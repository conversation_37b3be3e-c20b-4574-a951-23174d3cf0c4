/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.util;

import android.widget.ImageButton;
import android.widget.ImageView;

import com.sky.xposed.rimet.R;

/**
 * 图标工具类，确保图标能够正确显示
 */
public class IconUtil {

    private IconUtil() {
    }

    /**
     * 设置返回箭头图标
     */
    public static void setBackIcon(ImageView imageView) {
        imageView.setImageResource(R.drawable.ic_action_arrow_back);
    }

    /**
     * 设置确认图标
     */
    public static void setCheckIcon(ImageView imageView) {
        imageView.setImageResource(R.drawable.ic_action_check);
    }

    /**
     * 设置清除图标
     */
    public static void setClearIcon(ImageView imageView) {
        imageView.setImageResource(R.drawable.ic_action_clear);
    }

    /**
     * 设置更多选项（垂直三点）图标
     */
    public static void setMoreVertIcon(ImageView imageView) {
        imageView.setImageResource(R.drawable.ic_action_more_vert);
    }

    /**
     * 设置右箭头图标
     */
    public static void setRightArrowIcon(ImageView imageView) {
        imageView.setImageResource(R.drawable.ic_right_arrow);
    }
} 