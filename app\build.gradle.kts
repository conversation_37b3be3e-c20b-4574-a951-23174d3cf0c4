/*
 * Copyright (c) 2019 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
    id("com.android.application")
}

android {
    namespace = "com.sky.xposed.rimet"
    compileSdk = 35
    
    // 添加对Android 15 16KB页面大小的支持配置
    ndkVersion = "26.1.10909125"
    
    defaultConfig {
        applicationId = "com.sky.xposed.rimet"
        minSdk = 26
        targetSdk = 35
        versionCode = 30
        versionName = "1.4.4.3"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters += listOf("armeabi-v7a", "arm64-v8a") // "x86", "x86_64"
        }
        // 添加对Android 15 16KB页面大小的支持
        externalNativeBuild {
            cmake {
                arguments("-DANDROID_ARM_NEON=TRUE")
            }
        }
        manifestPlaceholders["package"] = "com.sky.xposed.rimet"
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    
    // 配置增量注解处理以提升构建性能
    androidComponents {
        onVariants(selector().all()) { variant ->
            variant.javaCompilation.annotationProcessor.arguments.put(
                "org.gradle.annotation.processing.incremental", "true"
            )
        }
    }
    
    signingConfigs {
        create("release") {
            storeFile = file("${rootDir}/keyStore/testkey.jks")
            storePassword = "android"
            keyAlias = "android"
            keyPassword = "android"
        }
        getByName("debug") {
            storeFile = file("${rootDir}/keyStore/testkey.jks")
            storePassword = "android"
            keyAlias = "android" 
            keyPassword = "android"
        }
    }
    
    buildTypes {
        release {
            isMinifyEnabled = true // 已开源
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            signingConfig = signingConfigs.getByName("release")
        }
        debug {
            isMinifyEnabled = false
            signingConfig = signingConfigs.getByName("debug")
        }
    }

    buildFeatures {
        buildConfig = true
    }

    androidResources {
        ignoreAssetsPattern = "!.svn:!.git:!.gitkeep:!.gitignore:!.ds_store:!*.scc:.*:*~:!CVS:!thumbs.db:!picasa.ini:!*~"
    }

    flavorDimensions += "default"

    productFlavors {
        create("demo") {
            // 测试渠道
            dimension = "default"
        }
        create("plugin") {
            // 正式发布渠道
            dimension = "default"
        }
    }

    sourceSets {
        getByName("main") {
            jniLibs.srcDirs("src/main/jniLibs")
        }
    }

    applicationVariants.all {
        val variant = this
        outputs.all {
            val output = this as com.android.build.gradle.internal.api.BaseVariantOutputImpl
            output.outputFileName = "${project.name}_${variant.flavorName}_v${variant.versionName}.apk"
        }
    }
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
    implementation("com.github.sky-wei:xposed-javax:1.2.0")
    implementation(project(":xposed-common:library"))
    implementation("com.squareup.picasso:picasso:2.71828")
    implementation(project(":xposed-frame:annotations"))
    implementation(project(":xposed-frame:core"))
    implementation(project(":xposed-frame:ui"))
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("androidx.annotation:annotation:1.7.0")
    implementation("com.tencent.bugly:crashreport:4.1.9")
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
    implementation("io.reactivex.rxjava2:rxandroid:2.1.1")
    implementation("io.reactivex.rxjava2:rxjava:2.2.21")
    implementation("com.squareup.retrofit2:adapter-rxjava2:2.9.0")
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
    implementation("com.jakewharton:disklrucache:2.0.2")
    compileOnly("de.robv.android.xposed:api:82")
    compileOnly("com.rover12421.AndroidHideApi:android:1.17")
    "demoImplementation"("org.apache.commons:commons-lang3:3.13.0")
    annotationProcessor(project(":xposed-frame:compiler"))
    
    // 添加单元测试依赖
    testImplementation("junit:junit:4.13.2")
    testImplementation("androidx.test:core:1.5.0")
    testImplementation("org.mockito:mockito-core:5.7.0")
}