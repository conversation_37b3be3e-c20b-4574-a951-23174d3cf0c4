Executing tasks: [:app:assembleDemoDebug] in project D:\xposed-rimet-resurrection

> Task :xposed-frame:core:preBuild UP-TO-DATE
> Task :app:preBuild UP-TO-DATE
> Task :xposed-frame:core:preDebugBuild UP-TO-DATE
> Task :xposed-frame:ui:preBuild UP-TO-DATE
> Task :xposed-common:library:preBuild UP-TO-DATE
> Task :xposed-common:library:preDebugBuild UP-TO-DATE
> Task :xposed-frame:ui:preDebugBuild UP-TO-DATE
> Task :app:preDemoDebugBuild UP-TO-DATE
> Task :app:mergeDemoDebugNativeDebugMetadata NO-SOURCE
> Task :xposed-frame:compiler:processResources NO-SOURCE
> Task :xposed-frame:ui:generateDebugResValues
> Task :xposed-common:library:writeDebugAarMetadata
> Task :xposed-frame:core:generateDebugResValues
> Task :xposed-common:library:generateDebugResValues
> Task :xposed-frame:ui:writeDebugAarMetadata
> Task :xposed-frame:core:writeDebugAarMetadata
> Task :app:generateDemoDebugBuildConfig
> Task :xposed-frame:core:generateDebugResources
> Task :app:generateDemoDebugResValues
> Task :xposed-frame:ui:generateDebugResources
> Task :xposed-common:library:generateDebugResources
> Task :xposed-frame:core:packageDebugResources
> Task :xposed-frame:core:extractDeepLinksDebug
> Task :app:generateDemoDebugResources
> Task :xposed-common:library:packageDebugResources
> Task :xposed-frame:ui:packageDebugResources
> Task :xposed-frame:ui:extractDeepLinksDebug
> Task :xposed-common:library:extractDeepLinksDebug
> Task :xposed-frame:core:processDebugManifest
> Task :xposed-common:library:processDebugManifest
> Task :xposed-frame:ui:processDebugManifest
Download https://repo.huaweicloud.com/repository/maven/com/android/tools/build/aapt2/8.7.0-12006047/aapt2-8.7.0-12006047.pom, took 166 ms
Downloading https://repo.huaweicloud.com/repository/maven/com/android/tools/build/aapt2/8.7.0-12006047/aapt2-8.7.0-12006047-windows.jar (1.57 MB / 2.05 MB)> Task :app:checkDemoDebugAarMetadata
Download https://repo.huaweicloud.com/repository/maven/com/android/tools/build/aapt2/8.7.0-12006047/aapt2-8.7.0-12006047-windows.jar, took 661 ms
> Task :app:mapDemoDebugSourceSetPaths
> Task :xposed-frame:core:compileDebugLibraryResources
> Task :xposed-frame:core:javaPreCompileDebug
> Task :xposed-frame:core:parseDebugLocalResources
> Task :xposed-frame:ui:parseDebugLocalResources
> Task :xposed-frame:ui:compileDebugLibraryResources
> Task :xposed-common:library:parseDebugLocalResources
> Task :xposed-common:library:compileDebugLibraryResources
> Task :xposed-common:library:javaPreCompileDebug
> Task :xposed-common:library:mergeDebugShaders
> Task :xposed-frame:core:mergeDebugShaders
> Task :xposed-common:library:compileDebugShaders NO-SOURCE
> Task :xposed-common:library:generateDebugAssets UP-TO-DATE
> Task :xposed-frame:core:compileDebugShaders NO-SOURCE
> Task :xposed-frame:core:generateDebugAssets UP-TO-DATE
> Task :xposed-common:library:packageDebugAssets
> Task :xposed-frame:core:packageDebugAssets
> Task :xposed-frame:core:generateDebugRFile
> Task :xposed-common:library:processDebugJavaRes NO-SOURCE
> Task :xposed-frame:core:processDebugJavaRes NO-SOURCE
> Task :xposed-frame:core:mergeDebugJniLibFolders
> Task :xposed-common:library:generateDebugRFile
> Task :xposed-frame:core:mergeDebugNativeLibs NO-SOURCE
> Task :xposed-common:library:mergeDebugJniLibFolders
> Task :xposed-frame:ui:javaPreCompileDebug
> Task :xposed-frame:core:copyDebugJniLibsProjectOnly
> Task :xposed-frame:ui:mergeDebugShaders
> Task :xposed-frame:ui:generateDebugRFile
> Task :xposed-frame:ui:compileDebugShaders NO-SOURCE
> Task :xposed-frame:ui:generateDebugAssets UP-TO-DATE
> Task :xposed-frame:ui:packageDebugAssets
> Task :xposed-frame:ui:processDebugJavaRes NO-SOURCE
> Task :xposed-frame:ui:mergeDebugJniLibFolders
> Task :xposed-frame:ui:mergeDebugNativeLibs NO-SOURCE
> Task :xposed-frame:ui:copyDebugJniLibsProjectOnly
> Task :xposed-frame:annotations:compileJava
> Task :xposed-frame:annotations:processResources NO-SOURCE
> Task :xposed-frame:annotations:classes
> Task :xposed-frame:annotations:jar
> Task :app:packageDemoDebugResources
> Task :app:createDemoDebugCompatibleScreenManifests
> Task :app:extractDeepLinksDemoDebug
> Task :app:parseDemoDebugLocalResources
> Task :app:mergeDemoDebugResources

> Task :app:processDemoDebugMainManifest
package="com.sky.xposed.rimet" found in source AndroidManifest.xml: D:\xposed-rimet-resurrection\app\src\main\AndroidManifest.xml.
Setting the namespace via the package attribute in the source AndroidManifest.xml is no longer supported, and the value is ignored.
Recommendation: remove package="com.sky.xposed.rimet" from the source AndroidManifest.xml: D:\xposed-rimet-resurrection\app\src\main\AndroidManifest.xml.

> Task :app:processDemoDebugManifest
> Task :app:mergeDemoDebugShaders
> Task :app:compileDemoDebugShaders NO-SOURCE
> Task :app:generateDemoDebugAssets UP-TO-DATE
> Task :app:mergeDemoDebugAssets
> Task :app:compressDemoDebugAssets
> Task :xposed-frame:compiler:compileJava
> Task :xposed-frame:compiler:classes
> Task :xposed-frame:compiler:jar
> Task :xposed-common:library:compileDebugJavaWithJavac
> Task :app:checkDemoDebugDuplicateClasses
> Task :app:javaPreCompileDemoDebug
> Task :app:mergeDemoDebugJniLibFolders
> Task :app:validateSigningDemoDebug
> Task :app:processDemoDebugManifestForPackage

> Task :xposed-common:library:compileDebugJavaWithJavac
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: 某些输入文件使用了未经检查或不安全的操作。
注: 有关详细信息, 请使用 -Xlint:unchecked 重新编译。

> Task :xposed-common:library:bundleLibRuntimeToJarDebug
> Task :xposed-common:library:mergeDebugNativeLibs NO-SOURCE
> Task :xposed-common:library:copyDebugJniLibsProjectOnly
> Task :xposed-common:library:bundleLibCompileToJarDebug
> Task :xposed-common:library:bundleLibRuntimeToDirDebug
> Task :app:writeDemoDebugAppMetadata
> Task :app:mergeDemoDebugNativeLibs
> Task :app:writeDemoDebugSigningConfigVersions

> Task :app:stripDemoDebugDebugSymbols
Unable to strip the following libraries, packaging them as they are: libBugly_Native.so.

> Task :xposed-frame:core:compileDebugJavaWithJavac
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: D:\xposed-rimet-resurrection\xposed-frame\core\src\main\java\com\sky\xposed\core\component\PluginManager.java使用了未经检查或不安全的操作。
注: 有关详细信息, 请使用 -Xlint:unchecked 重新编译。

> Task :xposed-frame:core:bundleLibCompileToJarDebug
> Task :xposed-frame:core:bundleLibRuntimeToJarDebug
> Task :xposed-frame:core:bundleLibRuntimeToDirDebug
> Task :app:processDemoDebugResources

> Task :xposed-frame:ui:compileDebugJavaWithJavac
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: 某些输入文件使用了未经检查或不安全的操作。
注: 有关详细信息, 请使用 -Xlint:unchecked 重新编译。

> Task :xposed-frame:ui:bundleLibRuntimeToJarDebug
> Task :xposed-frame:ui:bundleLibCompileToJarDebug
> Task :xposed-frame:ui:bundleLibRuntimeToDirDebug
> Task :app:desugarDemoDebugFileDependencies

> Task :app:compileDemoDebugJavaWithJavac
The following annotation processors are not incremental: compiler.jar (project :xposed-frame:compiler), auto-service-1.0-rc5.jar (com.google.auto.service:auto-service:1.0-rc5).
Make sure all annotation processors are incremental to improve your build speed.
注: 某些输入文件使用或覆盖了已过时的 API。
注: 有关详细信息, 请使用 -Xlint:deprecation 重新编译。
注: 某些输入文件使用了未经检查或不安全的操作。
注: 有关详细信息, 请使用 -Xlint:unchecked 重新编译。

> Task :app:dexBuilderDemoDebug
> Task :app:mergeDemoDebugGlobalSynthetics
> Task :app:processDemoDebugJavaRes
> Task :app:mergeProjectDexDemoDebug
> Task :app:mergeLibDexDemoDebug
> Task :app:mergeDemoDebugJavaResource
> Task :app:mergeExtDexDemoDebug
> Task :app:packageDemoDebug
> Task :app:createDemoDebugApkListingFileRedirect
> Task :app:assembleDemoDebug

[Incubating] Problems report is available at: file:///D:/xposed-rimet-resurrection/build/reports/problems/problems-report.html

BUILD SUCCESSFUL in 1m 18s
94 actionable tasks: 94 executed

Build Analyzer results available
