/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.util;

import com.sky.xposed.common.util.Alog;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 性能监控工具
 * 用于监控插件延迟加载的性能改善
 * 
 * Created by sky on 2024-01-01.
 */
public class PerformanceMonitor {
    
    private static final String TAG = "PerformanceMonitor";
    
    // 单例实例
    private static volatile PerformanceMonitor sInstance;
    
    // 时间戳记录
    private final Map<String, Long> mTimestamps = new ConcurrentHashMap<>();
    
    // 性能统计
    private final Map<String, PerformanceStats> mStats = new ConcurrentHashMap<>();
    
    /**
     * 性能统计数据
     */
    public static class PerformanceStats {
        public long startTime;
        public long endTime;
        public long duration;
        public String description;
        
        public PerformanceStats(long startTime, String description) {
            this.startTime = startTime;
            this.description = description;
        }
        
        public void finish(long endTime) {
            this.endTime = endTime;
            this.duration = endTime - startTime;
        }
        
        @Override
        public String toString() {
            return String.format("%s: %dms (开始: %d, 结束: %d)", 
                    description, duration, startTime, endTime);
        }
    }
    
    private PerformanceMonitor() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     */
    public static PerformanceMonitor getInstance() {
        if (sInstance == null) {
            synchronized (PerformanceMonitor.class) {
                if (sInstance == null) {
                    sInstance = new PerformanceMonitor();
                }
            }
        }
        return sInstance;
    }
    
    /**
     * 记录开始时间
     * @param key 标识符
     * @param description 描述
     */
    public void startTiming(String key, String description) {
        long currentTime = System.currentTimeMillis();
        mTimestamps.put(key, currentTime);
        mStats.put(key, new PerformanceStats(currentTime, description));
        
        Alog.d(TAG, "开始计时: " + key + " - " + description);
    }
    
    /**
     * 记录结束时间并计算耗时
     * @param key 标识符
     * @return 耗时（毫秒）
     */
    public long endTiming(String key) {
        long currentTime = System.currentTimeMillis();
        Long startTime = mTimestamps.get(key);
        
        if (startTime == null) {
            Alog.w(TAG, "未找到开始时间: " + key);
            return -1;
        }
        
        long duration = currentTime - startTime;
        
        PerformanceStats stats = mStats.get(key);
        if (stats != null) {
            stats.finish(currentTime);
            Alog.d(TAG, "结束计时: " + stats.toString());
        }
        
        return duration;
    }
    
    /**
     * 记录里程碑时间点
     * @param key 标识符
     * @param description 描述
     */
    public void recordMilestone(String key, String description) {
        long currentTime = System.currentTimeMillis();
        mTimestamps.put(key, currentTime);
        
        Alog.d(TAG, "里程碑: " + key + " - " + description + " (时间: " + currentTime + ")");
    }
    
    /**
     * 计算两个里程碑之间的时间差
     * @param startKey 开始里程碑
     * @param endKey 结束里程碑
     * @return 时间差（毫秒）
     */
    public long calculateDuration(String startKey, String endKey) {
        Long startTime = mTimestamps.get(startKey);
        Long endTime = mTimestamps.get(endKey);
        
        if (startTime == null || endTime == null) {
            Alog.w(TAG, "计算时间差失败 - 开始: " + startTime + ", 结束: " + endTime);
            return -1;
        }
        
        long duration = endTime - startTime;
        Alog.d(TAG, "时间差计算: " + startKey + " -> " + endKey + " = " + duration + "ms");
        
        return duration;
    }
    
    /**
     * 获取性能统计信息
     * @param key 标识符
     * @return 性能统计数据
     */
    public PerformanceStats getStats(String key) {
        return mStats.get(key);
    }
    
    /**
     * 输出所有性能统计信息
     */
    public void printAllStats() {
        Alog.d(TAG, "=== 性能统计报告 ===");
        
        for (Map.Entry<String, PerformanceStats> entry : mStats.entrySet()) {
            PerformanceStats stats = entry.getValue();
            if (stats.duration > 0) {
                Alog.d(TAG, entry.getKey() + ": " + stats.toString());
            }
        }
        
        Alog.d(TAG, "=== 报告结束 ===");
    }
    
    /**
     * 清除所有统计数据
     */
    public void clear() {
        mTimestamps.clear();
        mStats.clear();
        Alog.d(TAG, "性能统计数据已清除");
    }
    
    /**
     * 获取启动时间改善报告
     */
    public void generateStartupReport() {
        Alog.d(TAG, "=== 启动性能报告 ===");
        
        // 计算关键阶段的耗时
        long coreInitTime = calculateDuration("app_start", "core_init_complete");
        long pluginLoadTime = calculateDuration("core_init_complete", "immediate_plugins_loaded");
        long lazyLoadTime = calculateDuration("immediate_plugins_loaded", "lazy_load_complete");
        long totalTime = calculateDuration("app_start", "lazy_load_complete");
        
        if (coreInitTime > 0) {
            Alog.d(TAG, "核心初始化耗时: " + coreInitTime + "ms");
        }
        
        if (pluginLoadTime > 0) {
            Alog.d(TAG, "立即加载插件耗时: " + pluginLoadTime + "ms");
        }
        
        if (lazyLoadTime > 0) {
            Alog.d(TAG, "延迟加载插件耗时: " + lazyLoadTime + "ms");
        }
        
        if (totalTime > 0) {
            Alog.d(TAG, "总启动时间: " + totalTime + "ms");
            
            // 计算改善效果
            if (pluginLoadTime > 0 && totalTime > 0) {
                float improvement = ((float) pluginLoadTime / totalTime) * 100;
                Alog.d(TAG, "启动时间改善: 立即加载仅占总时间的 " + String.format("%.1f", improvement) + "%");
            }
        }
        
        Alog.d(TAG, "=== 报告结束 ===");
    }
}
