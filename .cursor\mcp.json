{"mcpServers": {"sequentialthinking": {"type": "sse", "url": "https://mcp-e882940e-fe89-46bf.api-inference.modelscope.net/sse"}, "mcp-taskmanager": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@kazuph/mcp-taskmanager", "--key", "f95a6ab0-95df-4e2f-b204-cd2d2a2d770a"]}, "deepwiki-sse": {"url": "https://mcp.deepwiki.com/sse"}, "fetch": {"command": "python", "args": ["-m", "mcp_server_fetch"]}, "filesystem": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C:/Users/<USER>/Desktop/"]}}}