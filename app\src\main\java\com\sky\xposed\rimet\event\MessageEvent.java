/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.event;

import com.sky.xposed.core.interfaces.XEvent;

import java.util.Collection;

/**
 * Created by sky on 2020-03-21.
 */
public interface MessageEvent extends XEvent {

    /**
     * 处理消息
     * @param cid
     * @param messages
     */
    void onHandlerMessage(String cid, Collection messages);
}
