/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.util;

import java.lang.reflect.Field;

/**
 * Created by sky on 18-3-13.
 */

public class FindUtil {

    public static Field firstOrNull(Field[] fields, Filter<Field> filter) {

        if (fields == null || filter == null) return null;

        for (Field field : fields) {

            if (filter.accept(field)) return field;
        }

        return null;
    }

    public interface Filter<T> {

        boolean accept(T t);
    }
}
