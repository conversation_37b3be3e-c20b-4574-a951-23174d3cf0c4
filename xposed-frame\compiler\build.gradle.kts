plugins {
    id("java-library")
    id("maven-publish")
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    implementation("com.google.auto.service:auto-service:1.1.1")
    annotationProcessor("com.google.auto.service:auto-service:1.1.1")
    implementation("com.squareup:javapoet:1.13.0")
    implementation("com.google.auto:auto-common:1.2.1")
    implementation("com.google.code.gson:gson:2.10.1")
    implementation(project(":xposed-frame:annotations"))
//    compileOnly(files(org.gradle.internal.jvm.Jvm.current().getToolsJar()))
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

afterEvaluate {
    publishing {
        publications {
            create<MavenPublication>("release") {
                from(components["java"])
                groupId = "com.github.jingcai-wei.xposed-frame"
                artifactId = "compiler"
                version = "1.1.1"
            }
        }
    }
} 