# 反定位拉回功能集成任务清单

## 第一阶段：基础设施准备

1. **环境配置与权限设置**
   - 添加必要的依赖项（Xposed API、位置服务相关库）
   - 在AndroidManifest.xml中添加位置权限（ACCESS_FINE_LOCATION, ACCESS_BACKGROUND_LOCATION）
   - 配置应用以支持Xposed模块集成（如需要）

2. **基础数据结构设计**
   - 创建FakeLoc类或类似结构，用于存储位置模拟状态
   - 设计持久化存储机制，保存用户设置（SharedPreferences）
   - 定义位置模拟相关的常量和配置参数

## 第二阶段：核心功能实现

3. **位置模拟基础实现**
   - 实现与LocationManager的交互接口
   - 创建位置模拟的核心方法（injectLocation等）
   - 实现位置数据的处理和转换功能

4. **位置监听器钩子实现**
   - 创建LocationServiceHook类（如使用Xposed）
   - 实现对系统位置服务的钩子方法
   - 添加对onLocationChanged等回调的拦截

5. **后台线程管理系统**
   - 实现类似loopThread的后台线程管理
   - 创建线程安全的启动和停止机制
   - 添加线程状态监控和异常处理

6. **位置广播机制**
   - 实现broadcastLocation方法
   - 创建与系统LocationManager通信的命令接口
   - 实现callOnLocationChanged等核心方法

## 第三阶段：UI界面开发

7. **设置界面设计**
   - 创建反定位拉回功能的设置项布局
   - 实现开关控件和相关UI元素
   - 添加功能说明和警告提示（关于可能的发热等）

8. **用户交互实现**
   - 实现开关状态的保存和读取
   - 添加开关状态变化的监听器
   - 实现用户操作的反馈机制（如Toast提示）

9. **状态指示器**
   - 添加功能运行状态的可视化指示
   - 实现简单的日志记录功能
   - 创建调试模式开关（可选）

## 第四阶段：集成与测试

10. **功能集成**
    - 将反定位拉回功能与现有位置模拟系统集成
    - 确保各组件之间的正确通信
    - 实现功能的启动和停止流程

11. **性能优化**
    - 优化后台线程的资源使用
    - 调整广播频率，平衡效果和性能
    - 实现智能休眠机制，减少不必要的CPU使用

12. **兼容性测试**
    - 在不同Android版本上测试功能
    - 验证在各种设备上的兼容性
    - 测试与其他位置相关功能的交互

13. **稳定性测试**
    - 长时间运行测试，确保无内存泄漏
    - 测试极端情况下的行为（如频繁切换开关）
    - 验证应用退出后的行为是否符合预期

## 第五阶段：发布与维护

14. **文档完善**
    - 编写功能使用说明
    - 记录实现细节和关键代码
    - 准备常见问题解答

15. **发布准备**
    - 最终功能验收测试
    - 准备发布说明
    - 规划后续维护和更新

## 技术要点与注意事项

- **性能考量**：反定位拉回功能会持续运行后台线程，需要特别注意电池消耗和发热问题
- **兼容性**：不同Android版本的LocationManager API可能有差异，需要做好兼容处理
- **用户体验**：清晰告知用户功能的作用和可能的副作用，避免误解
- **安全性**：确保位置模拟不会被滥用，考虑添加适当的安全措施
- **稳定性**：确保后台线程的可靠运行，处理好各种异常情况

## 可能需要引用的关键代码

### 1. FakeLoc类（核心数据结构）

```kotlin
object FakeLoc {
    // 是否启用反定位拉回功能
    var loopBroadcastLocation = false
    
    // 其他位置模拟相关属性
    var enable = false
    var latitude = 0.0
    var longitude = 0.0
    var altitude = 80.0
    var speed = 3.05
    var accuracy = 25.0f
    var lastLocation: Location? = null
    
    // 其他配置项...
}
```

### 2. 持久化存储（SharedPreferences扩展）

```kotlin
// 在Context扩展函数中定义
var Context.loopBroadcastlocation: Boolean
    get() = sharedPrefs.getBoolean("loopBroadcastLocation", FakeLoc.loopBroadcastLocation)
    set(value) = sharedPrefs.edit {
        putBoolean("loopBroadcastLocation", value)
        FakeLoc.loopBroadcastLocation = value
    }
```

### 3. 后台线程管理（MockServiceHelper）

```kotlin
object MockServiceHelper {
    private var loopThread: Thread? = null
    @Volatile private var isRunning = false
    
    private fun startLoopBroadcastLocation(locationManager: LocationManager) {
        val appContext = YourAppContext
        val delayTime = appContext.reportDuration.toLong()

        if(isRunning) return
        if(!appContext.loopBroadcastlocation) return

        isRunning = true
        loopThread = Thread {
            Log.d("MockServiceHelper", "loopBroadcast: Start")
            while(isRunning) {
                try {
                    broadcastLocation(locationManager)
                    Thread.sleep(delayTime)
                } catch (e: InterruptedException) {
                    Log.d("MockServiceHelper", "loopBroadcast: Stop")
                    break
                }
            }
        }
        loopThread!!.start()
    }

    private fun stopLoopBroadcastLocation() {
        isRunning = false
        loopThread?.interrupt()
        loopThread = null
    }
}
```

### 4. 位置广播机制

```kotlin
fun broadcastLocation(locationManager: LocationManager): Boolean {
    if (!::randomKey.isInitialized) {
        return false
    }
    val rely = Bundle()
    rely.putString("command_id", "broadcast_location")
    return locationManager.sendExtraCommand(PROVIDER_NAME, randomKey, rely)
}
```

### 5. 位置服务钩子（LocationServiceHook）

```kotlin
object LocationServiceHook {
    // 存储所有注册的位置监听器
    private val locationListeners = CopyOnWriteArrayList<Pair<String, IInterface>>()
    
    // 向所有监听器广播位置
    fun callOnLocationChanged() {
        locationListeners.forEach { listenerWithProvider ->
            val listener = listenerWithProvider.second
            var location = FakeLoc.lastLocation
            if (location == null) {
                location = Location(listenerWithProvider.first)
            }
            location = injectLocation(location)
            
            // 尝试调用监听器的onLocationChanged方法
            runCatching {
                val mOnLocationChanged = XposedHelpers.findMethodBestMatch(
                    listener.javaClass, "onLocationChanged", location)
                XposedBridge.invokeOriginalMethod(mOnLocationChanged, listener, arrayOf(location))
            }
        }
    }
    
    // 注入模拟位置
    private fun injectLocation(location: Location): Location {
        location.latitude = FakeLoc.latitude
        location.longitude = FakeLoc.longitude
        location.altitude = FakeLoc.altitude
        location.speed = FakeLoc.speed.toFloat()
        location.accuracy = FakeLoc.accuracy
        // 其他属性设置...
        return location
    }
}
```

### 6. UI界面实现（XML布局）

```xml
<LinearLayout
    android:id="@+id/loop_broadcast_location_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:padding="5dp"
    android:orientation="horizontal">
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center_vertical">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="反定位拉回"
            android:textColor="@color/title_color" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="解决位置拉回问题 可能导致发热"
            android:textSize="11sp" />
    </LinearLayout>
    <SwitchMaterial
        android:id="@+id/loop_broadcast_location_switch"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"/>
</LinearLayout>
```

### 7. UI交互实现（Activity/Fragment）

```kotlin
binding.loopBroadcastLocationSwitch.isChecked = requireContext().loopBroadcastlocation
binding.loopBroadcastLocationSwitch.setOnCheckedChangeListener { _, isChecked ->
    requireContext().loopBroadcastlocation = isChecked
    showToast("重启模拟生效")
}
```

### 8. 系统服务交互（RemoteCommandHandler）

```kotlin
// 在Xposed模块中处理命令
when (command) {
    "broadcast_location" -> {
        LocationServiceHook.callOnLocationChanged()
        return true
    }
    // 其他命令处理...
}
```
