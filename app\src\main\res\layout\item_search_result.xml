<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2019 The sky Authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:paddingLeft="8dp"
    android:paddingRight="8dp"
    android:gravity="center_vertical">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="14sp"
            android:textColor="#000000"/>

        <TextView
            android:id="@+id/tv_title_sub"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="true"
            android:ellipsize="end"
            android:textSize="12sp"
            android:layout_marginTop="1dp"
            android:textColor="#666666"/>

    </LinearLayout>

    <ImageView
        android:id="@+id/iv_check"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:visibility="invisible"
        android:src="@drawable/icon_affirm_selected"
        android:layout_marginLeft="8dp"/>

</LinearLayout>