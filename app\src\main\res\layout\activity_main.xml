<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2019 The sky Authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="#f5f5f5">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="35dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="20dp">

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_version"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="false"
                app:sky_itemMenuName="插件版本"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_ding_version"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="false"
                app:sky_itemMenuName="钉钉版本"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_dinglite_version"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="false"
                app:sky_itemMenuName="钉钉Lite版本"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_source"
                android:layout_marginTop="10dp"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="true"
                app:sky_itemMenuName="源码地址"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_download"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="true"
                app:sky_itemMenuName="下载地址"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_document"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="true"
                app:sky_itemMenuName="说明文档"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_love"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="true"
                app:sky_itemMenuName="爱心公益"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <com.sky.xposed.ui.view.ItemMenu
                android:id="@+id/im_about"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:onClick="onClick"
                app:sky_itemMenuLine="false"
                app:sky_itemMenuTextSize="16sp"
                app:sky_itemMenuTextColor="#333333"
                app:sky_itemMenuDescTextColor="#666666"
                app:sky_itemMenuShowMore="true"
                app:sky_itemMenuName="关于"
                android:background="@drawable/selector_item_menu"
                android:paddingLeft="5dp"
                android:paddingRight="5dp"
                />

            <TextView
                android:id="@+id/tv_support_version"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="10dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="5dp"
                android:lineSpacingExtra="5dp"
                android:textSize="14sp"
                android:textColor="#666666"
                android:padding="5dp"
                />

        </LinearLayout>
    </ScrollView>

</FrameLayout>