<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2019 The sky Authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.amap.api.maps2d.MapView
        android:id="@+id/map_view"
        android:layout_weight="1"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <ListView
            android:id="@+id/list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none"
            android:divider="@null"
            android:dividerHeight="1px">

        </ListView>

        <TextView
            android:id="@+id/tv_prompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            android:layout_gravity="center"
            android:text="暂无信息!"
            android:textSize="16sp"
            android:textColor="#666666"
            android:padding="8dp"
            />

    </FrameLayout>

</LinearLayout>