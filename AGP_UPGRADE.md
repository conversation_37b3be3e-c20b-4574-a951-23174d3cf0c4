# AGP 版本升级

## 已完成任务

- [x] 将根项目的AGP版本从8.4.0升级到8.7.0
- [x] 将xposed-frame子项目的AGP版本从8.4.0升级到8.7.0
- [x] 保持使用Gradle 8.6版本（已验证与AGP 8.7.0兼容）

## 升级注意事项

AGP 8.7.0相比8.4.0版本的主要变化：

1. 性能改进：构建速度优化
2. 构建配置API可能有所变化
3. Kotlin DSL语法可能有细微差异

## 后续工作

1. 如果遇到构建错误，需要查看AGP 8.7.0的官方迁移指南
2. 保持使用Gradle 8.6版本，其与AGP 8.7.0兼容
3. 测试构建过程，确保所有功能正常工作

## 参考资料

- [Android Gradle Plugin 发行说明](https://developer.android.com/studio/releases/gradle-plugin)
- [AGP 版本与Gradle版本兼容性](https://developer.android.com/build/releases/gradle-plugin) 