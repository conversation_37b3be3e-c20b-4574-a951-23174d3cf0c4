plugins {
    id("com.android.library")
    id("maven-publish")
}

android {
    namespace = "com.sky.xposed.frame.ui"
    compileSdk = 35
    
    defaultConfig {
        minSdk = 26
        
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }
    
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    tasks.withType<JavaCompile> {
        options.compilerArgs.add("-Xlint:deprecation")
    }

    publishing {
        singleVariant("release") {
            withSourcesJar()
            withJavadocJar()
        }
    }
}

publishing {
    publications {
        register<MavenPublication>("release") {
            groupId = "com.github.jingcai-wei.xposed-frame"
            artifactId = "ui"
            version = "1.1.2"
            
            afterEvaluate {
                from(components["release"])
            }
        }
    }
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
    implementation(project(":xposed-common:library"))
    implementation(project(":xposed-frame:core"))
    implementation("com.squareup.picasso:picasso:2.71828")
    compileOnly("de.robv.android.xposed:api:82")
    compileOnly("com.github.sky-wei:xposed-javax:1.2.0")
    compileOnly("com.rover12421.AndroidHideApi:android:1.17")
}