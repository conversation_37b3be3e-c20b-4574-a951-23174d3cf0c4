/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin.base;

import com.sky.xposed.common.util.Alog;
import com.sky.xposed.core.interfaces.XCoreManager;

/**
 * 支持延迟加载的BaseDingPlugin基类
 * 继承自BaseDingPlugin，实现LazyLoadable接口
 * 
 * Created by sky on 2024-01-01.
 */
public abstract class LazyBaseDingPlugin extends BaseDingPlugin implements LazyLoadable {
    
    private static final String TAG = "LazyBaseDingPlugin";
    
    // 延迟加载状态
    private volatile boolean mIsLazyLoaded = false;
    private volatile boolean mIsPreLoaded = false;
    
    public LazyBaseDingPlugin(XCoreManager coreManager) {
        super(coreManager);
    }
    
    @Override
    public Priority getLoadPriority() {
        // 默认为普通优先级，子类可以重写
        return Priority.NORMAL;
    }
    
    @Override
    public boolean canLazyLoad() {
        // 默认允许延迟加载，子类可以重写
        return true;
    }
    
    @Override
    public void onPreLazyLoad() {
        if (mIsPreLoaded) {
            return;
        }
        
        try {
            Alog.d(TAG, getPluginName() + " 开始预加载处理");
            
            // 执行轻量级的预处理
            doPreLazyLoad();
            
            mIsPreLoaded = true;
            Alog.d(TAG, getPluginName() + " 预加载处理完成");
            
        } catch (Throwable e) {
            Alog.e(TAG, getPluginName() + " 预加载处理异常", e);
        }
    }
    
    @Override
    public void onLazyLoadComplete() {
        if (mIsLazyLoaded) {
            return;
        }
        
        try {
            Alog.d(TAG, getPluginName() + " 开始延迟加载完成处理");
            
            // 执行延迟加载完成后的处理
            doLazyLoadComplete();
            
            mIsLazyLoaded = true;
            Alog.d(TAG, getPluginName() + " 延迟加载完成处理结束");
            
        } catch (Throwable e) {
            Alog.e(TAG, getPluginName() + " 延迟加载完成处理异常", e);
        }
    }
    
    @Override
    public String getPluginName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 执行预加载处理
     * 子类可以重写此方法进行轻量级的初始化
     */
    protected void doPreLazyLoad() {
        // 默认空实现
    }
    
    /**
     * 执行延迟加载完成后的处理
     * 子类可以重写此方法进行加载完成后的清理工作
     */
    protected void doLazyLoadComplete() {
        // 默认空实现
    }
    
    /**
     * 检查是否已经延迟加载
     */
    public boolean isLazyLoaded() {
        return mIsLazyLoaded;
    }
    
    /**
     * 检查是否已经预加载
     */
    public boolean isPreLoaded() {
        return mIsPreLoaded;
    }
    
    /**
     * 重写initialize方法，添加延迟加载支持
     */
    @Override
    public void initialize() {
        super.initialize();
        
        // 如果支持延迟加载，则添加到延迟加载器
        if (canLazyLoad()) {
            LazyPluginLoader.getInstance().addLazyPlugin(this);
            Alog.d(TAG, getPluginName() + " 已添加到延迟加载队列");
        } else {
            Alog.d(TAG, getPluginName() + " 不支持延迟加载，将立即初始化");
        }
    }
    
    /**
     * 获取延迟加载状态描述
     */
    public String getLazyLoadStatusDescription() {
        LazyPluginLoader.LoadState state = LazyPluginLoader.getInstance().getPluginState(getPluginName());
        return String.format("%s - 状态: %s, 预加载: %s, 延迟加载: %s", 
                getPluginName(), state, mIsPreLoaded, mIsLazyLoaded);
    }
    
    /**
     * 强制立即加载插件
     * 用于紧急情况下需要立即使用插件功能
     */
    public void forceLoad() {
        if (mIsLazyLoaded) {
            Alog.d(TAG, getPluginName() + " 已经加载，无需强制加载");
            return;
        }
        
        try {
            Alog.d(TAG, "强制加载插件: " + getPluginName());
            
            if (!mIsPreLoaded) {
                onPreLazyLoad();
            }
            
            hook();
            onLazyLoadComplete();
            
            Alog.d(TAG, "强制加载插件完成: " + getPluginName());
            
        } catch (Throwable e) {
            Alog.e(TAG, "强制加载插件失败: " + getPluginName(), e);
        }
    }
}
