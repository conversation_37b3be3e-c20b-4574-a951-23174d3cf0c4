/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet;

import android.app.Application;
import android.content.Context;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.sky.xposed.common.util.Alog;
import com.sky.xposed.common.util.ToastUtil;
import com.sky.xposed.core.XStore;
import com.sky.xposed.core.adapter.CoreListenerAdapter;
import com.sky.xposed.core.adapter.ThrowableAdapter;
import com.sky.xposed.core.component.ComponentFactory;
import com.sky.xposed.core.interfaces.XConfig;
import com.sky.xposed.core.interfaces.XCoreManager;
import com.sky.xposed.core.interfaces.XPlugin;
import com.sky.xposed.core.internal.CoreManager;
import com.sky.xposed.javax.XposedPlus;
import com.sky.xposed.javax.XposedUtil;
import com.sky.xposed.rimet.data.XPreferences;
import com.sky.xposed.rimet.plugin.LuckyPlugin;
import com.sky.xposed.rimet.util.FileUtil;
import com.sky.xposed.rimet.util.PerformanceMonitor;
import com.sky.xposed.ui.util.CoreUtil;
import com.sky.xposed.ui.util.DisplayUtil;
import com.squareup.picasso.Picasso;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;

import de.robv.android.xposed.IXposedHookLoadPackage;
import de.robv.android.xposed.XC_MethodHook;
import de.robv.android.xposed.XposedHelpers;
import de.robv.android.xposed.callbacks.XC_LoadPackage;

/**
 * Created by sky on 2019/3/14.
 */
public class Main implements IXposedHookLoadPackage {



    @Override
    public void handleLoadPackage(XC_LoadPackage.LoadPackageParam lpParam) throws Throwable {

        if (!XConstant.Rimet.PACKAGE_NAME.contains(lpParam.packageName)) return;

        // 初始化XposedPlus
        XposedPlus.setDefaultInstance(new XposedPlus.Builder(lpParam)
                .throwableCallback(new ThrowableAdapter())
                .build());
        Alog.i(this.getClass().getName(), String.format("Launch pacakage=%s processName=%s",lpParam.packageName,lpParam.processName));

        try {
            // 只保留一个钩子，防止重复加载
            XposedUtil.findMethod(
                    "com.alibaba.android.rimet.LauncherApplication", "onCreate")
                    .after(param -> handleLoadPackage(param, lpParam));

        } catch (Throwable throwable) {
            StringWriter sw = new StringWriter();
            throwable.printStackTrace(new PrintWriter(sw, true));
            Alog.setDebug(BuildConfig.DEBUG);
            Alog.i(this.getClass().getName(), sw.getBuffer().toString());
        }
    }

    /**
     * 处理加载的包
     *
     * @param param
     * @param lpParam
     * @throws Throwable
     */
    private void handleLoadPackage(
            XC_MethodHook.MethodHookParam param,
            XC_LoadPackage.LoadPackageParam lpParam) throws Throwable {
        Alog.i(this.getClass().getName(), "handleLoadPackage");
        Application application = (Application) param.thisObject;
        Context context = application.getApplicationContext();
        final String className = application.getClass().getName();
        Alog.i(this.getClass().getName(), String.format("handleLoadPackage: className=%s processName=%s",className,lpParam.processName));
        
        // 确保只有在LauncherApplication时才继续执行
        if (!"com.alibaba.android.rimet.LauncherApplication".equals(className)) {
            // 不需要处理
            Alog.i(this.getClass().getName(), "handleLoadPackage:" + className + " unneed");
            return;
        }

        // 记录开始时间用于性能统计
        final long startTime = System.currentTimeMillis();

        // 启动性能监控
        PerformanceMonitor monitor = PerformanceMonitor.getInstance();
        monitor.recordMilestone("app_start", "应用启动开始");

        XCoreManager coreManager = new CoreManager.Build(context)
                .setPluginPackageName(BuildConfig.APPLICATION_ID)
                .setProcessName(lpParam.processName)
                .setClassLoader(lpParam.classLoader)
                .setComponentFactory(new ComponentFactory() {
                    
                    // 获取preferences以检查开关状态
                    private XPreferences mPreferences = null;

                    @Override
                    protected List<Class<? extends XConfig>> getVersionData() {
                        return XStore.getConfigClass();
                    }

                    @RequiresApi(api = Build.VERSION_CODES.N)
                    @Override
                    protected List<Class<? extends XPlugin>> getPluginData() {
                        List<Class<? extends XPlugin>> plugins = XStore.getPluginClass();
                        // 无条件移除LuckyPlugin（之前是有条件移除）
                        plugins.remove(LuckyPlugin.class);
                        
                        // 尝试初始化mPreferences
                        if (mPreferences == null) {
                            try {
                                mPreferences = com.sky.xposed.rimet.data.XPreferences.create(context);
                                
                                // 初始化配置缓存
                                com.sky.xposed.rimet.data.cache.XPreferencesCacheFactory.initialize(context);
                                Alog.d(this.getClass().getName(), "配置缓存初始化成功");
                                
                                // 运行性能基准测试（可选，用于验证缓存效果）
                                try {
                                    String benchmarkResult = com.sky.xposed.rimet.data.cache.XPreferencesBenchmark.runQuickBenchmark(context);
                                    Alog.d(this.getClass().getName(), "配置缓存性能测试: " + benchmarkResult);
                                } catch (Exception e) {
                                    Alog.w(this.getClass().getName(), "配置缓存性能测试失败", e);
                                }
                            } catch (Exception e) {
                                Alog.e(this.getClass().getName(), "初始化preferences失败", e);
                            }
                        }
                        
                        // 移除AntiDetectionPlugin和DingDingPlugin插件
                        try {
                            Class antiDetectionClass = Class.forName("com.sky.xposed.rimet.plugin.AntiDetectionPlugin");
                            Class dingDingPluginClass = Class.forName("com.sky.xposed.rimet.plugin.DingDingPlugin");
                            Class recallMsgPluginClass = Class.forName("com.sky.xposed.rimet.plugin.RecallMsgPlugin");
                            Class debugPluginClass = Class.forName("com.sky.xposed.rimet.plugin.debug.DebugPlugin");
                            Class wifiPluginClass = Class.forName("com.sky.xposed.rimet.plugin.WifiPlugin");
                            Class stationPluginClass = Class.forName("com.sky.xposed.rimet.plugin.StationPlugin");
                            
                            plugins.remove(antiDetectionClass);
                            plugins.remove(dingDingPluginClass);
                            plugins.remove(recallMsgPluginClass);
                            
                            // 默认移除DebugPlugin，只有设置了开关才加载
                            plugins.remove(debugPluginClass);
                            
                            // 默认移除WifiPlugin和StationPlugin，只有设置了开关才加载
                            plugins.remove(wifiPluginClass);
                            plugins.remove(stationPluginClass);
                            
                            // 根据开关状态决定是否加载插件
                            if (mPreferences != null) {
                                // 判断是否加载DebugPlugin
                                boolean enableDebug = mPreferences
                                        .getBoolean(XConstant.Key.ENABLE_DEBUG_PLUGIN, false);
                                if (enableDebug) {
                                    // 如果启用了调试插件，则重新添加到插件列表
                                    plugins.add(debugPluginClass);
                                    Alog.d(this.getClass().getName(), "已启用DebugPlugin插件");
                                }
                                
                                // 判断是否加载WifiPlugin
                                boolean enableWifi = mPreferences
                                        .getBoolean(XConstant.Key.ENABLE_VIRTUAL_WIFI, false);
                                if (enableWifi) {
                                    // 如果启用了Wifi插件，则重新添加到插件列表
                                    plugins.add(wifiPluginClass);
                                    Alog.d(this.getClass().getName(), "已启用WifiPlugin插件");
                                }
                                
                                // 判断是否加载StationPlugin
                                boolean enableStation = mPreferences
                                        .getBoolean(XConstant.Key.ENABLE_VIRTUAL_STATION, false);
                                if (enableStation) {
                                    // 如果启用了基站插件，则重新添加到插件列表
                                    plugins.add(stationPluginClass);
                                    Alog.d(this.getClass().getName(), "已启用StationPlugin插件");
                                }
                            }
                            
                            Alog.d(this.getClass().getName(), "已禁用AntiDetectionPlugin、DingDingPlugin和RecallMsgPlugin插件");
                        } catch (ClassNotFoundException e) {
                            Alog.e(this.getClass().getName(), "禁用插件失败", e);
                        }
                        
                        StringBuilder lists = new StringBuilder();
                        plugins.forEach(
                                item -> {
                                    lists.append(item.getName()).append(",");
                                }
                        );
                        Alog.d(this.getClass().getName() + "init plugins:", lpParam.packageName + ": " + lists.toString());
                        return plugins;
                    }
                })
                .setCoreListener(new CoreListenerAdapter() {

                    @Override
                    public void onInitComplete(XCoreManager coreManager) {
                        super.onInitComplete(coreManager);

                        Alog.setDebug(BuildConfig.DEBUG);

                        final Context context = coreManager.getLoadPackage().getContext();

                        // 异步计算MD5，避免阻塞主线程
                        new Thread(() -> {
                            try {
                                // 保存当前版本MD5信息
                                String md5 = FileUtil.getFileMD5(new File(context.getApplicationInfo().sourceDir));
                                coreManager.getDefaultPreferences().putString(XConstant.Key.PACKAGE_MD5, md5);
                                Alog.d(getClass().getName(), "异步计算MD5完成: " + md5);
                            } catch (Exception e) {
                                Alog.e(getClass().getName(), "计算MD5异常", e);
                            }
                        }).start();
                        
                        // 延迟初始化UI组件
                        new Handler(Looper.getMainLooper()).postDelayed(() -> {
                            try {
                                // 初始化UI相关组件
                                CoreUtil.init(coreManager);
                                DisplayUtil.init(context);
                                ToastUtil.getInstance().init(context);

                                // 延迟初始化Picasso图片加载库
                                Picasso.setSingletonInstance(new Picasso.Builder(context).build());

                                Alog.d(getClass().getName(), "UI组件初始化完成, 耗时: " +
                                      (System.currentTimeMillis() - startTime) + "ms");

                                // 启动延迟加载插件的最终检查
                                startFinalLazyLoadCheck();

                            } catch (Exception e) {
                                Alog.e(getClass().getName(), "UI初始化异常", e);
                            }
                        }, 600);
                    }
                })
                .build();
        Alog.setDebug(BuildConfig.DEBUG);
        Alog.d(this.getClass().getName(), "hook all init success!");
        
        // 记录性能日志
        long coreInitTime = System.currentTimeMillis() - startTime;
        Alog.d(this.getClass().getName(), "核心初始化完成, 耗时: " + coreInitTime + "ms");

        // 记录核心初始化完成里程碑
        PerformanceMonitor.getInstance().recordMilestone("core_init_complete", "核心初始化完成");

        // 开始处理加载的包
        coreManager.loadPlugins();
    }

    /**
     * 启动最终的延迟加载检查
     * 确保所有延迟加载插件都已正确启动
     */
    private void startFinalLazyLoadCheck() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            try {
                // 使用反射检查LazyPluginLoader状态
                Class<?> lazyLoaderClass = Class.forName("com.sky.xposed.rimet.plugin.base.LazyPluginLoader");
                Object lazyLoader = lazyLoaderClass.getMethod("getInstance").invoke(null);

                // 检查是否正在加载
                boolean isLoading = (Boolean) lazyLoaderClass.getMethod("isLoading").invoke(lazyLoader);
                float progress = (Float) lazyLoaderClass.getMethod("getLoadProgress").invoke(lazyLoader);

                Alog.d(this.getClass().getName(), "延迟加载状态检查 - 正在加载: " + isLoading + ", 进度: " + (progress * 100) + "%");

                if (!isLoading && progress >= 1.0f) {
                    // 延迟加载完成，记录里程碑并生成报告
                    PerformanceMonitor.getInstance().recordMilestone("lazy_load_complete", "延迟加载完成");

                    // 延迟生成性能报告
                    new Handler(Looper.getMainLooper()).postDelayed(() -> {
                        PerformanceMonitor.getInstance().generateStartupReport();
                    }, 1000);
                } else if (!isLoading && progress < 1.0f) {
                    // 如果没有在加载但进度不完整，可能需要重新启动
                    Alog.w(this.getClass().getName(), "检测到延迟加载可能未完全启动，进度: " + (progress * 100) + "%");
                }

            } catch (Throwable e) {
                Alog.e(this.getClass().getName(), "延迟加载状态检查异常", e);
            }
        }, 3000); // 3秒后检查
    }
}
