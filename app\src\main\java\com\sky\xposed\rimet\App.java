/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet;

import android.app.Application;
import android.os.Handler;
import android.os.Looper;

import com.sky.xposed.common.util.Alog;
import com.sky.xposed.common.util.ToastUtil;
import com.sky.xposed.core.XStore;
import com.sky.xposed.core.adapter.CoreListenerAdapter;
import com.sky.xposed.core.component.ComponentFactory;
import com.sky.xposed.core.interfaces.XConfig;
import com.sky.xposed.core.interfaces.XCoreManager;
import com.sky.xposed.core.interfaces.XPlugin;
import com.sky.xposed.core.internal.CoreManager;
import com.sky.xposed.rimet.plugin.LuckyPlugin;
import com.sky.xposed.rimet.util.CrashHandler;
import com.sky.xposed.rimet.util.UiUtil;
import com.sky.xposed.rimet.data.XPreferences;
import com.sky.xposed.ui.util.CoreUtil;
import com.sky.xposed.ui.util.DisplayUtil;
import com.squareup.picasso.Picasso;

import java.util.List;

/**
 * Created by sky on 2019/3/26.
 */
public class App extends Application {

    private static App mApp;
    private XPreferences mPreferences;

    public static App getInstance() {
        return mApp;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        
        // 记录启动时间
        final long startTime = System.currentTimeMillis();
        
        // 初始化App
        mApp = this;
        mPreferences = XPreferences.create(this);
        
        // 基本初始化
        UiUtil.initialize(this);
        CrashHandler.getInstance().initialize(this);
        
        // 延迟初始化高德地图SDK隐私协议
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            try {
                // 只有当真正需要高德地图功能时才初始化
                boolean needLocationFunction = mPreferences.getBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, false) || 
                                              mPreferences.getBoolean(XConstant.Key.ENABLE_SMART_LOCATION, false);
                
                if (needLocationFunction) {
                    com.amap.api.location.AMapLocationClient.updatePrivacyShow(App.this, true, true);
                    com.amap.api.location.AMapLocationClient.updatePrivacyAgree(App.this, true);
                    Alog.d("App", "高德地图SDK隐私协议初始化完成");
                }
            } catch (Exception e) {
                Alog.e("App", "初始化高德地图SDK隐私协议失败", e);
            }
        }, 500);

        XCoreManager coreManager = new CoreManager.Build(this)
                .setProcessName(getPackageName())
                .setClassLoader(getClassLoader())
                .setPluginPackageName(getPackageName())
                .setComponentFactory(new ComponentFactory() {
                    @Override
                    protected List<Class<? extends XConfig>> getVersionData() {
                        return XStore.getConfigClass();
                    }

                    @Override
                    protected List<Class<? extends XPlugin>> getPluginData() {
                        List<Class<? extends XPlugin>> plugins = XStore.getPluginClass();
                        if (XConstant.Rimet.PACKAGE_NAME.get(1).equals(getPackageName())) {
                            plugins.remove(LuckyPlugin.class);
                        }
                        StringBuilder lists = new StringBuilder();
                        plugins.forEach(
                                item -> {
                                    lists.append(item.getName()).append(",");
                                }
                        );
                        Alog.d(this.getClass().getName() + "init plugins:", getPackageName() + ": " + lists.toString());
                        return plugins;
                    }
                })
                .setCoreListener(new CoreListenerAdapter())
                .build();

        // 初始化核心功能
        Alog.setDebug(BuildConfig.DEBUG);
        Alog.d(this.getClass().getName(), "hook all init success!");
        CoreUtil.init(coreManager);
        
        // 延迟初始化非关键组件
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            DisplayUtil.init(this);
            ToastUtil.getInstance().init(this);
            
            // 延迟初始化Picasso (只有在真正需要图片加载时才初始化)
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                Picasso.setSingletonInstance(new Picasso.Builder(App.this).build());
                Alog.d("App", "Picasso图片加载库初始化完成");
            }, 1000);
            
            Alog.d("App", "App初始化完成, 总耗时: " + (System.currentTimeMillis() - startTime) + "ms");
        }, 300);
    }
}
