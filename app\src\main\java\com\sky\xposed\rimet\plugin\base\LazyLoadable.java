/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin.base;

/**
 * 延迟加载接口
 * 标识可以延迟加载的插件
 * 
 * Created by sky on 2024-01-01.
 */
public interface LazyLoadable {
    
    /**
     * 插件加载优先级
     * 数值越小优先级越高，关键插件应该返回较小的值
     */
    enum Priority {
        CRITICAL(0),    // 关键插件，立即加载
        HIGH(1),        // 高优先级，延迟100ms加载
        NORMAL(2),      // 普通优先级，延迟500ms加载
        LOW(3);         // 低优先级，延迟1000ms加载
        
        private final int value;
        
        Priority(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
        
        /**
         * 获取延迟时间（毫秒）
         */
        public long getDelayMs() {
            switch (this) {
                case CRITICAL:
                    return 0;
                case HIGH:
                    return 100;
                case NORMAL:
                    return 500;
                case LOW:
                    return 1000;
                default:
                    return 500;
            }
        }
    }
    
    /**
     * 获取插件的加载优先级
     * @return 优先级枚举
     */
    Priority getLoadPriority();
    
    /**
     * 是否可以延迟加载
     * @return true表示可以延迟加载，false表示必须立即加载
     */
    boolean canLazyLoad();
    
    /**
     * 延迟加载前的预处理
     * 在插件被延迟加载之前调用，可以进行一些轻量级的初始化
     */
    default void onPreLazyLoad() {
        // 默认空实现
    }
    
    /**
     * 延迟加载完成后的回调
     * 在插件延迟加载完成后调用
     */
    default void onLazyLoadComplete() {
        // 默认空实现
    }
    
    /**
     * 获取插件名称，用于日志记录
     * @return 插件名称
     */
    default String getPluginName() {
        return this.getClass().getSimpleName();
    }
}
