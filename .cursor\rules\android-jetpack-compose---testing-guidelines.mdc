---
description: Defines testing guidelines for Android Jetpack Compose components, ViewModels, and UseCases.  # 定义 Android Jetpack Compose 组件、ViewModels 和 UseCases 的测试指南。
globs: app/src/test/java/com/package/**/*.kt
alwaysApply: false
---
---
description: Defines testing guidelines for Android Jetpack Compose components, ViewModels, and UseCases.
globs: app/src/test/java/com/package/**/*.kt
---
# 为ViewModel和UseCase编写单元测试
- Write unit tests for ViewModels and UseCases.
# 使用Compose测试框架实现UI测试
- Implement UI tests using Compose testing framework.
# 测试中使用fake repository
- Use fake repositories for testing.
# 实现合适的测试覆盖率
- Implement proper test coverage.
# 使用合适的测试协程调度器
- Use proper testing coroutine dispatchers.