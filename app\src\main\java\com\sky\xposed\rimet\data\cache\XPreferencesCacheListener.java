/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data.cache;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * 配置变更监听器
 * 监听SharedPreferences的变更并更新缓存
 */
public class XPreferencesCacheListener implements SharedPreferences.OnSharedPreferenceChangeListener {

    private final Context mContext;
    private XPreferencesCache mCache;

    public XPreferencesCacheListener(Context context) {
        mContext = context.getApplicationContext();
    }

    /**
     * 开始监听配置变更
     */
    public void startListening() {
        try {
            mCache = XPreferencesCacheFactory.getCache(mContext);
            if (mCache != null) {
                SharedPreferences sp = mCache.getOriginalPreferences().getSharedPreferences();
                sp.registerOnSharedPreferenceChangeListener(this);
                android.util.Log.d("XPreferencesCacheListener", "开始监听配置变更");
            }
        } catch (Exception e) {
            android.util.Log.e("XPreferencesCacheListener", "启动配置监听失败", e);
        }
    }

    /**
     * 停止监听配置变更
     */
    public void stopListening() {
        try {
            if (mCache != null) {
                SharedPreferences sp = mCache.getOriginalPreferences().getSharedPreferences();
                sp.unregisterOnSharedPreferenceChangeListener(this);
                android.util.Log.d("XPreferencesCacheListener", "停止监听配置变更");
            }
        } catch (Exception e) {
            android.util.Log.e("XPreferencesCacheListener", "停止配置监听失败", e);
        }
    }

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
        if (mCache == null || key == null) {
            return;
        }

        try {
            // 当配置发生变更时，刷新缓存中的对应项
            if (sharedPreferences.contains(key)) {
                Object value = sharedPreferences.getAll().get(key);
                if (value != null) {
                    // 直接更新缓存中的值
                    if (value instanceof String) {
                        mCache.putString(key, (String) value);
                    } else if (value instanceof Boolean) {
                        mCache.putBoolean(key, (Boolean) value);
                    } else if (value instanceof Integer) {
                        mCache.putInt(key, (Integer) value);
                    } else if (value instanceof Long) {
                        mCache.putLong(key, (Long) value);
                    } else if (value instanceof Float) {
                        mCache.putFloat(key, (Float) value);
                    }
                    
                    android.util.Log.d("XPreferencesCacheListener", 
                        "配置变更: key=" + key + ", value=" + value + ", type=" + value.getClass().getSimpleName());
                }
            } else {
                // 配置项被删除，从缓存中移除
                mCache.remove(key);
                android.util.Log.d("XPreferencesCacheListener", "配置删除: key=" + key);
            }
        } catch (Exception e) {
            android.util.Log.e("XPreferencesCacheListener", "处理配置变更失败: key=" + key, e);
        }
    }
}