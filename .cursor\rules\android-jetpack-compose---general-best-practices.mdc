---
description: Applies general best practices for Android Jetpack Compose development within the main application code.  # 在主要应用程序代码中应用 Android Jetpack Compose 开发的一般最佳实践。
globs: app/src/main/java/com/package/**/*.kt
alwaysApply: false
---
---
description: Applies general best practices for Android Jetpack Compose development within the main application code.
globs: app/src/main/java/com/package/**/*.kt
---
# 在保持代码整洁原则的同时，适应现有项目架构
- Adapt to existing project architecture while maintaining clean code principles.
# 遵循Material Design 3的设计规范和组件
- Follow Material Design 3 guidelines and components.
# 实现包含domain、data和presentation分层的clean architecture
- Implement clean architecture with domain, data, and presentation layers.
# 异步操作使用Kotlin协程和Flow
- Use Kotlin coroutines and Flow for asynchronous operations.
# 使用Hilt实现依赖注入
- Implement dependency injection using Hilt.
# 遵循ViewModel和UI State的单向数据流
- Follow unidirectional data flow with ViewModel and UI State.
# 屏幕管理使用Compose navigation
- Use Compose navigation for screen management.
# 正确实现state hoisting和composition
- Implement proper state hoisting and composition.