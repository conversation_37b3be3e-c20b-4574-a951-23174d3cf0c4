/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data.cache;

import android.content.Context;

/**
 * XPreferencesCache工厂类
 * 负责管理缓存实例的创建和初始化
 */
public class XPreferencesCacheFactory {

    private static volatile boolean sInitialized = false;
    private static XPreferencesCache sCacheInstance;

    /**
     * 初始化配置缓存
     * 应在应用启动时调用
     */
    public static synchronized void initialize(Context context) {
        if (sInitialized) {
            return;
        }
        
        try {
            sCacheInstance = XPreferencesCache.getInstance(context);
            sCacheInstance.initialize();
            sInitialized = true;
        } catch (Exception e) {
            // 初始化失败时记录日志，但不抛出异常
            // 这样可以确保应用正常运行，只是没有缓存优化
            android.util.Log.e("XPreferencesCacheFactory", "初始化配置缓存失败", e);
        }
    }

    /**
     * 获取缓存实例
     * 如果未初始化，会自动初始化
     */
    public static XPreferencesCache getCache(Context context) {
        if (!sInitialized) {
            initialize(context);
        }
        return sCacheInstance;
    }

    /**
     * 检查是否已初始化
     */
    public static boolean isInitialized() {
        return sInitialized;
    }

    /**
     * 重置缓存（主要用于测试）
     */
    public static synchronized void reset() {
        sInitialized = false;
        sCacheInstance = null;
    }
}