/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data.cache;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.sky.xposed.rimet.XConstant;
import com.sky.xposed.rimet.data.XPreferences;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SharedPreferences缓存管理类
 * 通过内存缓存减少频繁的磁盘读取操作，提升性能
 */
public class XPreferencesCache {

    private static volatile XPreferencesCache sInstance;
    private final XPreferences mPreferences;
    private final Map<String, Object> mCache;
    private final Set<String> mPreloadKeys;
    private volatile boolean mInitialized = false;
    private XPreferencesCacheListener mListener;

    // 常用配置键列表 - 这些键会在初始化时预加载到缓存
    private static final String[] COMMON_KEYS = {
        // 插件开关相关
        XConstant.Key.ENABLE_DEBUG_PLUGIN,
        XConstant.Key.ENABLE_VIRTUAL_WIFI,
        XConstant.Key.ENABLE_VIRTUAL_STATION,
        XConstant.Key.ENABLE_VIRTUAL_LOCATION,
        XConstant.Key.ENABLE_SMART_LOCATION,
        XConstant.Key.ENABLE_FAST_LUCKY,
        XConstant.Key.ENABLE_LUCKY,
        XConstant.Key.ENABLE_RECALL,
        
        // 基础配置
        XConstant.Key.PACKAGE_NAME,
        XConstant.Key.PACKAGE_MD5,
        XConstant.Key.LAST_ALIAS,
        
        // 定位相关
        XConstant.Key.LOCATION_LATITUDE,
        XConstant.Key.LOCATION_LONGITUDE,
        XConstant.Key.LOCATION_ADDRESS,
        
        // 智能定位相关
        XConstant.Key.SMART_LOCATION_WORK_START_TIME,
        XConstant.Key.SMART_LOCATION_WORK_END_TIME,
        
        // 红包相关
        XConstant.Key.LUCKY_DELAYED,
        
        // Wifi相关
        XConstant.Key.WIFI_INFO,
        XConstant.Key.WIFI_ENABLED,
        XConstant.Key.WIFI_STATE,
        XConstant.Key.WIFI_SS_ID,
        XConstant.Key.WIFI_BSS_ID,
        XConstant.Key.WIFI_MAC_ADDRESS,
        XConstant.Key.WIFI_SCAN_RESULT,
        
        // 基站相关
        XConstant.Key.STATION_INFO,
        XConstant.Key.STATION_MCC,
        XConstant.Key.STATION_MNC,
        XConstant.Key.STATION_LAC,
        XConstant.Key.STATION_CELL_ID
    };

    private XPreferencesCache(Context context) {
        mPreferences = XPreferences.create(context);
        mCache = new ConcurrentHashMap<>();
        mPreloadKeys = new HashSet<>();
        
        // 添加常用键到预加载列表
        for (String key : COMMON_KEYS) {
            mPreloadKeys.add(key);
        }
    }

    /**
     * 获取单例实例
     */
    public static XPreferencesCache getInstance(Context context) {
        if (sInstance == null) {
            synchronized (XPreferencesCache.class) {
                if (sInstance == null) {
                    sInstance = new XPreferencesCache(context.getApplicationContext());
                }
            }
        }
        return sInstance;
    }

    /**
     * 初始化缓存 - 预加载常用配置项
     */
    public synchronized void initialize() {
        if (mInitialized) {
            return;
        }
        
        // 批量预加载常用配置项
        preloadCommonKeys();
        
        // 启动配置变更监听
        startConfigListener();
        
        mInitialized = true;
    }

    /**
     * 预加载常用配置键
     */
    private void preloadCommonKeys() {
        SharedPreferences sp = mPreferences.getSharedPreferences();
        Map<String, ?> allValues = sp.getAll();
        
        // 只预加载存在的键值
        for (String key : mPreloadKeys) {
            if (allValues.containsKey(key)) {
                Object value = allValues.get(key);
                if (value != null) {
                    mCache.put(key, value);
                }
            }
        }
    }

    /**
     * 批量预加载指定的配置键
     */
    public void preloadKeys(String... keys) {
        if (keys == null || keys.length == 0) {
            return;
        }
        
        SharedPreferences sp = mPreferences.getSharedPreferences();
        for (String key : keys) {
            if (!TextUtils.isEmpty(key) && sp.contains(key)) {
                Object value = sp.getAll().get(key);
                if (value != null) {
                    mCache.put(key, value);
                    mPreloadKeys.add(key);
                }
            }
        }
    }

    /**
     * 获取字符串值（带缓存）
     */
    public String getString(String key, String defValue) {
        if (!mInitialized) {
            initialize();
        }
        
        Object cachedValue = mCache.get(key);
        if (cachedValue instanceof String) {
            return (String) cachedValue;
        }
        
        // 缓存中没有，从SharedPreferences读取并缓存
        String value = mPreferences.getString(key, defValue);
        mCache.put(key, value);
        return value;
    }

    public String getString(String key) {
        return getString(key, "");
    }

    /**
     * 获取布尔值（带缓存）
     */
    public boolean getBoolean(String key, boolean defValue) {
        if (!mInitialized) {
            initialize();
        }
        
        Object cachedValue = mCache.get(key);
        if (cachedValue instanceof Boolean) {
            return (Boolean) cachedValue;
        }
        
        // 缓存中没有，从SharedPreferences读取并缓存
        boolean value = mPreferences.getBoolean(key, defValue);
        mCache.put(key, value);
        return value;
    }

    public boolean getBoolean(String key) {
        return getBoolean(key, false);
    }

    /**
     * 获取整数值（带缓存）
     */
    public int getInt(String key, int defValue) {
        if (!mInitialized) {
            initialize();
        }
        
        Object cachedValue = mCache.get(key);
        if (cachedValue instanceof Integer) {
            return (Integer) cachedValue;
        }
        
        // 缓存中没有，从SharedPreferences读取并缓存
        int value = mPreferences.getInt(key, defValue);
        mCache.put(key, value);
        return value;
    }

    public int getInt(String key) {
        return getInt(key, 0);
    }

    /**
     * 获取长整数值（带缓存）
     */
    public long getLong(String key, long defValue) {
        if (!mInitialized) {
            initialize();
        }
        
        Object cachedValue = mCache.get(key);
        if (cachedValue instanceof Long) {
            return (Long) cachedValue;
        }
        
        // 缓存中没有，从SharedPreferences读取并缓存
        long value = mPreferences.getLong(key, defValue);
        mCache.put(key, value);
        return value;
    }

    public long getLong(String key) {
        return getLong(key, 0L);
    }

    /**
     * 获取浮点数值（带缓存）
     */
    public float getFloat(String key, float defValue) {
        if (!mInitialized) {
            initialize();
        }
        
        Object cachedValue = mCache.get(key);
        if (cachedValue instanceof Float) {
            return (Float) cachedValue;
        }
        
        // 缓存中没有，从SharedPreferences读取并缓存
        float value = mPreferences.getFloat(key, defValue);
        mCache.put(key, value);
        return value;
    }

    public float getFloat(String key) {
        return getFloat(key, 0.0f);
    }

    /**
     * 保存字符串值并更新缓存
     */
    public void putString(String key, String value) {
        mPreferences.putString(key, value);
        mCache.put(key, value);
    }

    /**
     * 保存布尔值并更新缓存
     */
    public void putBoolean(String key, boolean value) {
        mPreferences.putBoolean(key, value);
        mCache.put(key, value);
    }

    /**
     * 保存整数值并更新缓存
     */
    public void putInt(String key, int value) {
        mPreferences.putInt(key, value);
        mCache.put(key, value);
    }

    /**
     * 保存长整数值并更新缓存
     */
    public void putLong(String key, long value) {
        mPreferences.putLong(key, value);
        mCache.put(key, value);
    }

    /**
     * 保存浮点数值并更新缓存
     */
    public void putFloat(String key, float value) {
        mPreferences.putFloat(key, value);
        mCache.put(key, value);
    }

    /**
     * 移除配置项并更新缓存
     */
    public void remove(String key) {
        mPreferences.remove(key);
        mCache.remove(key);
    }

    /**
     * 清除所有配置并清空缓存
     */
    public void clear() {
        mPreferences.clear();
        mCache.clear();
    }

    /**
     * 刷新缓存 - 重新从SharedPreferences加载所有缓存的键
     */
    public void refreshCache() {
        Set<String> cachedKeys = new HashSet<>(mCache.keySet());
        mCache.clear();
        
        SharedPreferences sp = mPreferences.getSharedPreferences();
        Map<String, ?> allValues = sp.getAll();
        
        // 重新加载之前缓存的键
        for (String key : cachedKeys) {
            if (allValues.containsKey(key)) {
                Object value = allValues.get(key);
                if (value != null) {
                    mCache.put(key, value);
                }
            }
        }
    }

    /**
     * 批量获取配置项
     */
    public Map<String, Object> getBatch(String... keys) {
        Map<String, Object> result = new HashMap<>();
        if (keys != null) {
            for (String key : keys) {
                if (mCache.containsKey(key)) {
                    result.put(key, mCache.get(key));
                } else {
                    // 从SharedPreferences读取
                    SharedPreferences sp = mPreferences.getSharedPreferences();
                    if (sp.contains(key)) {
                        Object value = sp.getAll().get(key);
                        if (value != null) {
                            mCache.put(key, value);
                            result.put(key, value);
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        return new CacheStats(mCache.size(), mPreloadKeys.size(), mInitialized);
    }

    /**
     * 启动配置变更监听
     */
    private void startConfigListener() {
        try {
            // 注意：这里需要传入Context，但XPreferences没有提供Context
            // 所以暂时不启动监听器，可以在需要时手动调用refreshCache()
            // mListener = new XPreferencesCacheListener(context);
            // mListener.startListening();
        } catch (Exception e) {
            android.util.Log.w("XPreferencesCache", "启动配置监听失败", e);
        }
    }

    /**
     * 停止配置变更监听
     */
    public void stopConfigListener() {
        if (mListener != null) {
            mListener.stopListening();
            mListener = null;
        }
    }

    /**
     * 获取原始的XPreferences对象（用于兼容性）
     */
    public XPreferences getOriginalPreferences() {
        return mPreferences;
    }

    /**
     * 缓存统计信息
     */
    public static class CacheStats {
        public final int cachedItemCount;
        public final int preloadKeyCount;
        public final boolean initialized;

        CacheStats(int cachedItemCount, int preloadKeyCount, boolean initialized) {
            this.cachedItemCount = cachedItemCount;
            this.preloadKeyCount = preloadKeyCount;
            this.initialized = initialized;
        }

        @Override
        public String toString() {
            return "CacheStats{" +
                    "cachedItems=" + cachedItemCount +
                    ", preloadKeys=" + preloadKeyCount +
                    ", initialized=" + initialized +
                    '}';
        }
    }
}