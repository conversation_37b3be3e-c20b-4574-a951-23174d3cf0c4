/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin.test;

import com.sky.xposed.annotations.APlugin;
import com.sky.xposed.common.util.Alog;
import com.sky.xposed.core.interfaces.XCoreManager;
import com.sky.xposed.rimet.plugin.base.LazyBaseDingPlugin;
import com.sky.xposed.rimet.plugin.base.LazyLoadable;

/**
 * 延迟加载测试插件
 * 用于测试延迟加载机制的功能
 * 
 * Created by sky on 2024-01-01.
 */
@APlugin
public class LazyLoadTestPlugin extends LazyBaseDingPlugin {
    
    private static final String TAG = "LazyLoadTestPlugin";
    
    public LazyLoadTestPlugin(XCoreManager coreManager) {
        super(coreManager);
    }
    
    @Override
    public Priority getLoadPriority() {
        // 设置为普通优先级进行测试
        return Priority.NORMAL;
    }
    
    @Override
    public boolean canLazyLoad() {
        // 允许延迟加载
        return true;
    }
    
    @Override
    protected void doPreLazyLoad() {
        Alog.d(TAG, "测试插件预加载处理开始");
        
        // 模拟轻量级初始化
        try {
            Thread.sleep(50); // 模拟50ms的初始化时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Alog.d(TAG, "测试插件预加载处理完成");
    }
    
    @Override
    protected void doLazyLoadComplete() {
        Alog.d(TAG, "测试插件延迟加载完成处理开始");
        
        // 模拟加载完成后的处理
        try {
            Thread.sleep(30); // 模拟30ms的完成处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Alog.d(TAG, "测试插件延迟加载完成处理结束");
    }
    
    @Override
    public void hook() {
        Alog.d(TAG, "测试插件Hook开始执行");
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 模拟Hook操作
            simulateHookOperations();
            
            long endTime = System.currentTimeMillis();
            Alog.d(TAG, "测试插件Hook执行完成，耗时: " + (endTime - startTime) + "ms");
            
        } catch (Throwable e) {
            Alog.e(TAG, "测试插件Hook执行异常", e);
        }
    }
    
    /**
     * 模拟Hook操作
     */
    private void simulateHookOperations() {
        // 模拟一些Hook操作的耗时
        try {
            // 模拟查找类和方法的时间
            Thread.sleep(100);
            
            Alog.d(TAG, "模拟Hook操作 - 查找目标类");
            Thread.sleep(50);
            
            Alog.d(TAG, "模拟Hook操作 - 设置方法拦截");
            Thread.sleep(80);
            
            Alog.d(TAG, "模拟Hook操作 - 注册回调");
            Thread.sleep(30);
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            Alog.w(TAG, "模拟Hook操作被中断");
        }
    }
    
    @Override
    public void initialize() {
        super.initialize();
        Alog.d(TAG, "测试插件初始化完成 - " + getLazyLoadStatusDescription());
    }
    
    @Override
    public void release() {
        super.release();
        Alog.d(TAG, "测试插件释放资源");
    }
    
    /**
     * 获取插件状态信息
     */
    public String getStatusInfo() {
        return String.format("LazyLoadTestPlugin - 预加载: %s, 延迟加载: %s, 优先级: %s", 
                isPreLoaded(), isLazyLoaded(), getLoadPriority());
    }
    
    /**
     * 测试强制加载功能
     */
    public void testForceLoad() {
        Alog.d(TAG, "测试强制加载功能");
        forceLoad();
    }
}
