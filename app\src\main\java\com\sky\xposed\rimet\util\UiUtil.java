/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.util;

import android.content.Context;

import com.sky.xposed.common.util.Alog;

/**
 * UI工具类，用于初始化UI相关功能
 */
public class UiUtil {

    private static boolean sInitialized = false;

    private UiUtil() {
    }

    /**
     * 初始化UI相关功能
     * @param context 应用上下文
     */
    public static void initialize(Context context) {
        if (sInitialized) return;

        try {
            // UI相关初始化操作可以在这里进行
            sInitialized = true;
            Alog.d("UiUtil", "UI初始化完成");
        } catch (Exception e) {
            Alog.e("UiUtil", "UI初始化失败", e);
        }
    }

    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    public static boolean isInitialized() {
        return sInitialized;
    }
} 