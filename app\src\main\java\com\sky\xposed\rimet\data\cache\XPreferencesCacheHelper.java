/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data.cache;

import android.content.Context;

import com.sky.xposed.rimet.XConstant;

import java.util.Map;

/**
 * XPreferencesCache辅助类
 * 提供常用的配置读取方法和批量操作
 */
public class XPreferencesCacheHelper {

    /**
     * 批量获取所有插件开关状态
     */
    public static Map<String, Object> getAllPluginSwitches(Context context) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        return cache.getBatch(
            XConstant.Key.ENABLE_DEBUG_PLUGIN,
            XConstant.Key.ENABLE_VIRTUAL_WIFI,
            XConstant.Key.ENABLE_VIRTUAL_STATION,
            XConstant.Key.ENABLE_VIRTUAL_LOCATION,
            XConstant.Key.ENABLE_SMART_LOCATION,
            XConstant.Key.ENABLE_FAST_LUCKY,
            XConstant.Key.ENABLE_LUCKY,
            XConstant.Key.ENABLE_RECALL
        );
    }

    /**
     * 批量获取定位相关配置
     */
    public static Map<String, Object> getLocationConfigs(Context context) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        return cache.getBatch(
            XConstant.Key.ENABLE_VIRTUAL_LOCATION,
            XConstant.Key.LOCATION_LATITUDE,
            XConstant.Key.LOCATION_LONGITUDE,
            XConstant.Key.LOCATION_ADDRESS,
            XConstant.Key.ENABLE_SMART_LOCATION,
            XConstant.Key.SMART_LOCATION_WORK_START_TIME,
            XConstant.Key.SMART_LOCATION_WORK_END_TIME
        );
    }

    /**
     * 批量获取Wifi相关配置
     */
    public static Map<String, Object> getWifiConfigs(Context context) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        return cache.getBatch(
            XConstant.Key.ENABLE_VIRTUAL_WIFI,
            XConstant.Key.WIFI_INFO,
            XConstant.Key.WIFI_ENABLED,
            XConstant.Key.WIFI_STATE,
            XConstant.Key.WIFI_SS_ID,
            XConstant.Key.WIFI_BSS_ID,
            XConstant.Key.WIFI_MAC_ADDRESS,
            XConstant.Key.WIFI_SCAN_RESULT
        );
    }

    /**
     * 批量获取基站相关配置
     */
    public static Map<String, Object> getStationConfigs(Context context) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        return cache.getBatch(
            XConstant.Key.ENABLE_VIRTUAL_STATION,
            XConstant.Key.STATION_INFO,
            XConstant.Key.STATION_MCC,
            XConstant.Key.STATION_MNC,
            XConstant.Key.STATION_LAC,
            XConstant.Key.STATION_CELL_ID
        );
    }

    /**
     * 预加载插件相关的配置键
     * 建议在插件初始化时调用
     */
    public static void preloadPluginConfigs(Context context, String... additionalKeys) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        
        // 预加载常用的插件配置键
        String[] commonKeys = {
            XConstant.Key.ENABLE_DEBUG_PLUGIN,
            XConstant.Key.ENABLE_VIRTUAL_WIFI,
            XConstant.Key.ENABLE_VIRTUAL_STATION,
            XConstant.Key.ENABLE_VIRTUAL_LOCATION,
            XConstant.Key.ENABLE_SMART_LOCATION,
            XConstant.Key.PACKAGE_MD5,
            XConstant.Key.LUCKY_DELAYED
        };
        
        cache.preloadKeys(commonKeys);
        
        // 预加载额外的键
        if (additionalKeys != null && additionalKeys.length > 0) {
            cache.preloadKeys(additionalKeys);
        }
    }

    /**
     * 检查缓存是否正常工作
     */
    public static boolean isCacheWorking(Context context) {
        try {
            XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
            XPreferencesCache.CacheStats stats = cache.getCacheStats();
            return stats.initialized && stats.cachedItemCount > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取缓存性能统计信息
     */
    public static String getCacheStatsInfo(Context context) {
        try {
            XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
            XPreferencesCache.CacheStats stats = cache.getCacheStats();
            return String.format("缓存状态: 已初始化=%s, 缓存项数=%d, 预加载键数=%d", 
                stats.initialized ? "是" : "否", 
                stats.cachedItemCount, 
                stats.preloadKeyCount);
        } catch (Exception e) {
            return "获取缓存统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 刷新所有缓存
     * 当配置在外部被修改时调用
     */
    public static void refreshAllCache(Context context) {
        try {
            XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
            cache.refreshCache();
        } catch (Exception e) {
            android.util.Log.e("XPreferencesCacheHelper", "刷新缓存失败", e);
        }
    }
}