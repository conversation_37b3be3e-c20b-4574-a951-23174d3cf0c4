<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2020 The sky Authors.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5">

    <TextView
        android:id="@+id/tv_out_info"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:scrollbars="vertical"
        android:fadeScrollbars="false"
        android:textSize="14sp"
        android:textColor="#333333"
        android:lineSpacingExtra="4dp"
        android:padding="5dp"
        />

</FrameLayout>