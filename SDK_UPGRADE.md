# SDK 版本升级任务列表

## 已完成任务

- [x] 将主应用模块(app)的minSdk从21升级到26
- [x] 将主应用模块(app)的targetSdk从33升级到35
- [x] 将主应用模块(app)的compileSdk从33升级到35
- [x] 将xposed-frame/core模块的minSdk从21升级到26
- [x] 将xposed-frame/core模块的compileSdk从33升级到35
- [x] 将xposed-frame/ui模块的minSdk从21升级到26
- [x] 将xposed-frame/ui模块的compileSdk从33升级到35
- [x] 将xposed-common/library模块的minSdk从21升级到26
- [x] 将xposed-common/library模块的compileSdk从33升级到35
- [x] 将xposed-common/app模块的minSdk从21升级到26
- [x] 将xposed-common/app模块的compileSdk和targetSdk从27升级到35
- [x] 将xposed-frame/app模块的minSdk从21升级到26
- [x] 将xposed-frame/app模块的compileSdk和targetSdk从29升级到35
- [x] 将AGP版本从8.4.0升级到8.7.0
- [x] 使用Gradle 8.13版本（与AGP 8.7.0兼容）
- [x] 修复单元测试配置问题（添加JUnit依赖和修正AndroidManifest.xml中的minSdkVersion）
- [x] 检查代码中是否有使用了API 21-25的功能，需要进行兼容性调整
- [x] 更新项目依赖库版本，以支持最新的targetSdk 35

## 待办任务

- [ ] 针对targetSdk 35的新特性或行为变更，进行相应的代码适配
- [ ] 验证AGP 8.7.0与新的SDK版本兼容性

## 注意事项

- minSdk设置为26意味着应用将不再支持Android 8.0以下的设备
- targetSdk设置为35意味着应用将针对Android 15进行优化
- 需要确保所有第三方库都兼容minSdk 26及以上版本
- AGP 8.7.0可能引入新的构建特性和优化
- Gradle 8.6与AGP 8.7.0兼容，可以正常使用
- 发现以下需要进行兼容性调整的API使用（已完成）
  - 存储权限相关：需要更新`READ_EXTERNAL_STORAGE`和`WRITE_EXTERNAL_STORAGE`权限的请求逻辑
  - 权限请求：更新`PermissionUtil`类，移除不必要的版本检查
  - UI组件：简化`TitleView`和`SwitchItemView`等组件中的版本检查代码
  - 版本检查：移除`if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)`等已不再需要的版本检查

## 更新的依赖库

以下依赖库版本已更新以支持targetSdk 35：

1. `androidx.annotation:annotation`: 1.6.0 → 1.7.0
2. `com.tencent.bugly:crashreport`: 3.1.0 → 4.1.9
3. `com.squareup.retrofit2:retrofit`: 2.5.0 → 2.9.0
4. `io.reactivex.rxjava2:rxjava`: 2.2.12 → 2.2.21
5. `com.squareup.retrofit2:adapter-rxjava2`: 2.5.0 → 2.9.0
6. `com.squareup.retrofit2:converter-gson`: 2.5.0 → 2.9.0
7. `org.apache.commons:commons-lang3`: 3.7 → 3.13.0
8. `org.mockito:mockito-core`: 4.0.0 → 5.7.0
9. 更新了库的版本号：
   - `xposed-common` 库版本从 1.0.5 升级到 1.0.6
   - `xposed-frame:core` 和 `xposed-frame:ui` 库版本从 1.1.1 升级到 1.1.2