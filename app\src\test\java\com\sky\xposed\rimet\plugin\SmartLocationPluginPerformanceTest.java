/*
 * Copyright (c) 2023 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

/**
 * SmartLocationPlugin性能测试
 * 验证优化后的性能改进
 */
public class SmartLocationPluginPerformanceTest {

    @Test
    public void testTimeToMinutesPerformance() {
        // 测试时间转换函数的性能
        long startTime = System.nanoTime();
        
        // 模拟大量时间转换操作
        for (int i = 0; i < 10000; i++) {
            String timeStr = String.format("%02d:%02d", i % 24, i % 60);
            int minutes = timeToMinutes(timeStr);
            assertTrue("时间转换结果应该有效", minutes >= 0);
        }
        
        long endTime = System.nanoTime();
        long duration = (endTime - startTime) / 1000000; // 转换为毫秒
        
        System.out.println("10000次时间转换耗时: " + duration + "ms");
        assertTrue("时间转换性能应该良好", duration < 100); // 应该在100ms内完成
    }
    
    @Test
    public void testTimeFormatValidation() {
        // 测试时间格式验证的性能
        long startTime = System.nanoTime();
        
        String[] validTimes = {"07:00", "18:30", "23:59", "00:00"};
        String[] invalidTimes = {"25:00", "12:60", "abc", "", null};
        
        for (int i = 0; i < 1000; i++) {
            for (String time : validTimes) {
                assertTrue("有效时间格式应该通过验证", isValidTimeFormat(time));
            }
            for (String time : invalidTimes) {
                assertFalse("无效时间格式应该被拒绝", isValidTimeFormat(time));
            }
        }
        
        long endTime = System.nanoTime();
        long duration = (endTime - startTime) / 1000000;
        
        System.out.println("时间格式验证耗时: " + duration + "ms");
        assertTrue("时间格式验证性能应该良好", duration < 50);
    }
    
    /**
     * 时间字符串转换为分钟数（从SmartLocationPlugin复制）
     */
    private int timeToMinutes(String timeStr) {
        try {
            String[] parts = timeStr.split(":");
            if (parts.length == 2) {
                int hours = Integer.parseInt(parts[0]);
                int minutes = Integer.parseInt(parts[1]);
                return hours * 60 + minutes;
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return -1;
    }
    
    /**
     * 验证时间格式（从SmartLocationPlugin复制）
     */
    private boolean isValidTimeFormat(String timeStr) {
        if (timeStr == null || timeStr.isEmpty()) return false;
        return timeStr.matches("^([01]?[0-9]|2[0-3]):([0-5][0-9])$");
    }
}