rootProject.name = "xposed-rimet-resurrection"

include(":app")
include(":xposed-common:library")
include(":xposed-frame:annotations")
include(":xposed-frame:core")
include(":xposed-frame:compiler")
include(":xposed-frame:ui")

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        maven { url = uri("https://maven.aliyun.com/repository/public") }
        maven { url = uri("https://maven.aliyun.com/repository/google") }
        maven { url = uri("https://maven.aliyun.com/repository/gradle-plugin") }
        maven { url = uri("https://repo.huaweicloud.com/repository/maven") }
        maven { url = uri("https://maven.aliyun.com/repository/jcenter") }
        maven { url = uri("https://jitpack.io") }
        google()
        mavenCentral()
    }
} 