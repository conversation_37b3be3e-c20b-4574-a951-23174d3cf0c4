---
description: Outlines performance optimization guidelines for Android Jetpack Compose applications.  # 概述 Android Jetpack Compose 应用程序的性能优化指南。
globs: app/src/main/java/com/package/**/*.kt
alwaysApply: false
---
---
description: Outlines performance optimization guidelines for Android Jetpack Compose applications.
globs: app/src/main/java/com/package/**/*.kt
---
# 使用合适的key最小化重组
- Minimize recomposition using proper keys.
# 使用LazyColumn和LazyRow实现合适的懒加载
- Use proper lazy loading with LazyColumn and LazyRow.
# 实现高效的图片加载
- Implement efficient image loading.
# 使用合适的状态管理防止不必要的更新
- Use proper state management to prevent unnecessary updates.
# 遵循正确的生命周期感知
- Follow proper lifecycle awareness.
# 实现合适的内存管理
- Implement proper memory management.
# 使用合适的后台处理方式
- Use proper background processing.