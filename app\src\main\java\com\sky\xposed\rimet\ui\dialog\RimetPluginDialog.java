/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.ui.dialog;

import android.app.Activity;
import android.os.Bundle;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.PopupMenu;

import com.sky.xposed.rimet.R;
import com.sky.xposed.rimet.util.IconUtil;
import com.sky.xposed.ui.base.BasePluginDialog;
import com.sky.xposed.ui.view.PluginFrameLayout;
import com.sky.xposed.ui.view.TitleView;

/**
 * 自定义的钉钉插件对话框，使用Vector图标
 */
public abstract class RimetPluginDialog extends BasePluginDialog {

    @Override
    public abstract void createView(PluginFrameLayout frameView);

    /**
     * 显示返回
     */
    @Override
    public void showBack() {
        showBack(R.drawable.ic_action_clear);
    }

    /**
     * 显示返回,显示指定返回图标
     * @param resId
     */
    @Override
    public void showBack(int resId) {

        TitleView titleView = getTitleView();
        titleView.showBack();
        
        // 设置监听
        titleView.setOnBackEventListener(view1 -> {
            if (onBackEvent()) {
                setResult(Activity.RESULT_CANCELED, null);
                dismiss();
            }
        });

        // 设置图标 - 使用Vector图标
        IconUtil.setClearIcon(titleView.getBackView());
    }

    /**
     * 在标题栏上添加ImageButton
     * @param resId
     * @param listener
     */
    @Override
    public void addMoreImageView(int resId, View.OnClickListener listener) {

        ImageButton iBtn = getTitleView().addMoreImageButton();
        iBtn.setOnClickListener(listener);

        // 根据不同的资源ID设置相应的图标
        if (resId == R.drawable.ic_action_arrow_back) {
            IconUtil.setBackIcon(iBtn);
        } else if (resId == R.drawable.ic_action_check) {
            IconUtil.setCheckIcon(iBtn);
        } else if (resId == R.drawable.ic_action_clear) {
            IconUtil.setClearIcon(iBtn);
        } else if (resId == R.drawable.ic_action_more_vert) {
            IconUtil.setMoreVertIcon(iBtn);
        } else if (resId == R.drawable.ic_right_arrow) {
            IconUtil.setRightArrowIcon(iBtn);
        } else {
            // 如果是其他资源ID，尝试直接设置
            iBtn.setImageResource(resId);
        }
    }

    /**
     * 显示更多菜单
     */
    @Override
    public void showMoreMenu() {

        ImageButton mMoreButton = getTitleView().addMoreImageButton();
        mMoreButton.setOnClickListener(v -> {
            // 打开更多菜单
            final PopupMenu popupMenu = new PopupMenu(getContext(), mMoreButton, Gravity.RIGHT);
            onCreateMoreMenu(popupMenu.getMenu());
            popupMenu.setOnMenuItemClickListener(item -> {
                onMoreItemSelected(item);
                return true;
            });
            popupMenu.show();
        });

        // 设置图标 - 使用Vector图标
        IconUtil.setMoreVertIcon(mMoreButton);
    }
} 