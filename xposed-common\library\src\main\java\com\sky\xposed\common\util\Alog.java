/*
 * Copyright (c) 2018 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.common.util;

import android.os.Process;
import android.util.Log;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 优化的日志工具类，提供更细粒度的控制和更高效的实现
 * Created by sky on 2018/8/8.
 */
public class Alog {

    public final static String TAG = "Xposed";

    // 日志级别定义
    public static final int LEVEL_VERBOSE = 1;
    public static final int LEVEL_DEBUG = 2;
    public static final int LEVEL_INFO = 3;
    public static final int LEVEL_WARN = 4;
    public static final int LEVEL_ERROR = 5;
    public static final int LEVEL_NONE = 10;
    
    // 当前日志级别，可根据需要调整
    private static volatile int sLogLevel = LEVEL_INFO; // 默认只显示INFO及以上级别
    private static volatile boolean sDebug = false;
    
    // 性能日志记录控制
    private static volatile boolean sEnablePerformanceLog = false;
    
    // 日志过滤控制
    private static final Set<String> sEnabledTags = new HashSet<>();
    private static volatile boolean sUseTagFilter = false;
    
    // 日志计时缓存
    private static final ConcurrentHashMap<String, Long> sTimingCache = new ConcurrentHashMap<>();
    
    // 日志采样率控制
    private static final ConcurrentHashMap<String, Integer> sLogCounter = new ConcurrentHashMap<>();
    private static volatile int sSampleRate = 1; // 默认每条日志都输出
    
    // 日期格式化器 ThreadLocal缓存
    private static final ThreadLocal<SimpleDateFormat> sDateFormatThreadLocal = 
            ThreadLocal.withInitial(() -> new SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()));
    
    // 配置控制 =================================================================
    
    /**
     * 检查是否为调试模式
     */
    public static boolean isDebug() {
        return sDebug;
    }

    /**
     * 设置调试模式
     * 当设置为调试模式时，自动将日志级别调整为DEBUG
     */
    public static void setDebug(boolean debug) {
        sDebug = debug;
        // 调试模式下自动调整日志级别为DEBUG
        if (debug) {
            sLogLevel = LEVEL_DEBUG;
        } else {
            sLogLevel = LEVEL_INFO;
        }
    }
    
    /**
     * 设置日志级别
     */
    public static void setLogLevel(int level) {
        sLogLevel = level;
    }
    
    /**
     * 启用/禁用性能日志
     */
    public static void enablePerformanceLog(boolean enable) {
        sEnablePerformanceLog = enable;
    }
    
    /**
     * 设置日志采样率
     * @param rate 采样率，比如设置为10则每10条同类日志只输出1条
     */
    public static void setSampleRate(int rate) {
        if (rate < 1) rate = 1;
        sSampleRate = rate;
    }
    
    /**
     * 添加要输出日志的标签
     * 启用标签过滤后，只有添加的标签才会输出日志
     */
    public static void addEnabledTag(String tag) {
        synchronized (sEnabledTags) {
            sEnabledTags.add(tag);
        }
    }
    
    /**
     * 启用标签过滤
     * @param enable true表示只输出已添加标签的日志，false表示输出所有日志
     */
    public static void enableTagFilter(boolean enable) {
        sUseTagFilter = enable;
    }
    
    /**
     * 清除所有设置，恢复默认状态
     */
    public static void reset() {
        sLogLevel = LEVEL_INFO;
        sDebug = false;
        sEnablePerformanceLog = false;
        sSampleRate = 1;
        sUseTagFilter = false;
        synchronized (sEnabledTags) {
            sEnabledTags.clear();
        }
        sTimingCache.clear();
        sLogCounter.clear();
    }
    
    /**
     * 检查指定级别的日志是否会被输出
     * 可用于在构建复杂日志消息前进行检查，避免不必要的字符串操作
     */
    public static boolean isLoggable(int level) {
        return level >= sLogLevel;
    }
    
    /**
     * 检查指定标签和级别的日志是否会被输出
     */
    public static boolean isLoggable(String tag, int level) {
        if (level < sLogLevel) return false;
        
        if (sUseTagFilter) {
            synchronized (sEnabledTags) {
                return sEnabledTags.contains(tag);
            }
        }
        return true;
    }
    
    // 基本日志方法 =================================================================
    
    public static void i(String msg) {
        if (isLoggable(LEVEL_INFO)) {
            i(TAG, msg);
        }
    }
    
    public static void i(String tag, String msg) {
        if (isLoggable(tag, LEVEL_INFO) && shouldLog(tag, msg)) {
            Log.i(tag, msg);
        }
    }
    
    public static void i(String tag, String msg, Throwable tr) {
        if (isLoggable(tag, LEVEL_INFO)) {
            Log.i(tag, msg, tr);
        }
    }
    
    public static void d(String msg) {
        if (isLoggable(LEVEL_DEBUG)) {
            d(TAG, msg);
        }
    }
    
    public static void d(String tag, String msg) {
        if (isLoggable(tag, LEVEL_DEBUG) && shouldLog(tag, msg)) {
            Log.d(tag, msg);
        }
    }
    
    public static void d(String tag, String msg, Throwable tr) {
        if (isLoggable(tag, LEVEL_DEBUG)) {
            Log.d(tag, msg, tr);
        }
    }
    
    public static void e(String msg) {
        if (isLoggable(LEVEL_ERROR)) {
            e(TAG, msg);
        }
    }
    
    public static void e(String msg, Throwable tr) {
        if (isLoggable(LEVEL_ERROR)) {
            e(TAG, msg, tr);
        }
    }
    
    public static void e(String tag, String msg) {
        if (isLoggable(tag, LEVEL_ERROR)) {
            Log.e(tag, msg);
        }
    }
    
    public static void e(String tag, String msg, Throwable tr) {
        if (isLoggable(tag, LEVEL_ERROR)) {
            Log.e(tag, msg, tr);
        }
    }
    
    public static void v(String msg) {
        if (isLoggable(LEVEL_VERBOSE)) {
            v(TAG, msg);
        }
    }
    
    public static void v(String tag, String msg) {
        if (isLoggable(tag, LEVEL_VERBOSE) && shouldLog(tag, msg)) {
            Log.v(tag, msg);
        }
    }
    
    public static void v(String tag, String msg, Throwable tr) {
        if (isLoggable(tag, LEVEL_VERBOSE)) {
            Log.v(tag, msg, tr);
        }
    }
    
    public static void w(String msg) {
        if (isLoggable(LEVEL_WARN)) {
            w(TAG, msg);
        }
    }
    
    public static void w(String tag, String msg) {
        if (isLoggable(tag, LEVEL_WARN) && shouldLog(tag, msg)) {
            Log.w(tag, msg);
        }
    }
    
    public static void w(String tag, String msg, Throwable tr) {
        if (isLoggable(tag, LEVEL_WARN)) {
            Log.w(tag, msg, tr);
        }
    }
    
    // 格式化日志方法 =================================================================
    
    public static void d(String tag, String format, Object... args) {
        if (isLoggable(tag, LEVEL_DEBUG)) {
            String msg = formatMessage(format, args);
            if (shouldLog(tag, msg)) {
                Log.d(tag, msg);
            }
        }
    }
    
    public static void d(String format, Object... args) {
        d(TAG, format, args);
    }
    
    public static void i(String tag, String format, Object... args) {
        if (isLoggable(tag, LEVEL_INFO)) {
            String msg = formatMessage(format, args);
            if (shouldLog(tag, msg)) {
                Log.i(tag, msg);
            }
        }
    }
    
    public static void i(String format, Object... args) {
        i(TAG, format, args);
    }
    
    public static void e(String tag, String format, Object... args) {
        if (isLoggable(tag, LEVEL_ERROR)) {
            Log.e(tag, formatMessage(format, args));
        }
    }
    
    public static void e(String format, Object... args) {
        e(TAG, format, args);
    }
    
    public static void w(String tag, String format, Object... args) {
        if (isLoggable(tag, LEVEL_WARN)) {
            String msg = formatMessage(format, args);
            if (shouldLog(tag, msg)) {
                Log.w(tag, msg);
            }
        }
    }
    
    public static void w(String format, Object... args) {
        w(TAG, format, args);
    }
    
    public static void v(String tag, String format, Object... args) {
        if (isLoggable(tag, LEVEL_VERBOSE)) {
            String msg = formatMessage(format, args);
            if (shouldLog(tag, msg)) {
                Log.v(tag, msg);
            }
        }
    }
    
    public static void v(String format, Object... args) {
        v(TAG, format, args);
    }
    
    // 条件日志方法 =================================================================
    
    /**
     * 仅当条件为true时输出调试日志
     */
    public static void dIf(boolean condition, String tag, String msg) {
        if (condition) {
            d(tag, msg);
        }
    }
    
    /**
     * 仅当条件为true时输出调试日志
     */
    public static void dIf(boolean condition, String tag, String format, Object... args) {
        if (condition) {
            d(tag, format, args);
        }
    }
    
    /**
     * 仅当条件为true时输出信息日志
     */
    public static void iIf(boolean condition, String tag, String msg) {
        if (condition) {
            i(tag, msg);
        }
    }
    
    /**
     * 仅当条件为true时输出错误日志
     */
    public static void eIf(boolean condition, String tag, String msg) {
        if (condition) {
            e(tag, msg);
        }
    }
    
    // 性能日志方法 =================================================================
    
    /**
     * 记录性能日志
     */
    public static void performance(String tag, String action, long timeMs) {
        if (sEnablePerformanceLog && isLoggable(LEVEL_DEBUG)) {
            Log.d(tag, String.format("[性能] %s: %dms", action, timeMs));
        }
    }
    
    /**
     * 开始记录操作耗时
     */
    public static void beginTiming(String operation) {
        if (sEnablePerformanceLog && isLoggable(LEVEL_DEBUG)) {
            sTimingCache.put(operation, System.currentTimeMillis());
        }
    }
    
    /**
     * 结束记录操作耗时并输出日志
     */
    public static void endTiming(String tag, String operation) {
        if (sEnablePerformanceLog && isLoggable(LEVEL_DEBUG)) {
            Long startTime = sTimingCache.remove(operation);
            if (startTime != null) {
                long duration = System.currentTimeMillis() - startTime;
                Log.d(tag, String.format("[性能] %s: %dms", operation, duration));
            }
        }
    }
    
    /**
     * 输出当前线程信息，用于调试线程问题
     */
    public static void thread(String tag, String msg) {
        if (isLoggable(tag, LEVEL_DEBUG)) {
            Thread t = Thread.currentThread();
            Log.d(tag, String.format("%s [线程:%s,ID:%d,优先级:%d]", 
                    msg, t.getName(), t.getId(), Process.getThreadPriority(Process.myTid())));
        }
    }
    
    // 工具方法 =================================================================
    
    /**
     * 格式化消息，优化String.format性能
     */
    private static String formatMessage(String format, Object... args) {
        if (args == null || args.length == 0) {
            return format;
        }
        
        try {
            return String.format(Locale.US, format, args);
        } catch (Exception e) {
            return format + " [格式化错误: " + e.getMessage() + "]";
        }
    }
    
    /**
     * 检查是否应该记录此日志（基于采样率）
     */
    private static boolean shouldLog(String tag, String msg) {
        if (sSampleRate <= 1) {
            return true;
        }
        
        // 对于错误日志，始终输出
        if (sLogLevel >= LEVEL_ERROR) {
            return true;
        }
        
        // 计算消息的唯一键
        String key = tag + ":" + (msg != null ? msg.hashCode() : 0);
        
        // 获取当前计数
        Integer count = sLogCounter.compute(key, (k, v) -> {
            if (v == null) return 1;
            return v + 1;
        });
        
        // 根据采样率决定是否输出
        return count % sSampleRate == 0;
    }
    
    /**
     * 获取当前时间的格式化字符串
     */
    public static String getCurrentTimeString() {
        return sDateFormatThreadLocal.get().format(new Date());
    }
}
