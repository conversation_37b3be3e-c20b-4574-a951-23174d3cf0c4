/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data.cache;

import android.content.Context;

import com.sky.xposed.rimet.XConstant;

/**
 * XPreferencesCache测试类
 * 用于验证缓存功能和性能
 */
public class XPreferencesCacheTest {

    /**
     * 测试缓存性能
     */
    public static void testCachePerformance(Context context) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        
        // 测试读取性能
        long startTime = System.currentTimeMillis();
        
        // 模拟频繁读取常用配置
        for (int i = 0; i < 1000; i++) {
            cache.getBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, false);
            cache.getBoolean(XConstant.Key.ENABLE_VIRTUAL_WIFI, false);
            cache.getBoolean(XConstant.Key.ENABLE_SMART_LOCATION, false);
            cache.getString(XConstant.Key.LOCATION_LATITUDE, "");
            cache.getString(XConstant.Key.LOCATION_LONGITUDE, "");
            cache.getInt(XConstant.Key.LUCKY_DELAYED, 500);
        }
        
        long endTime = System.currentTimeMillis();
        long cacheTime = endTime - startTime;
        
        // 测试原始SharedPreferences性能
        startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 1000; i++) {
            cache.getOriginalPreferences().getBoolean(XConstant.Key.ENABLE_VIRTUAL_LOCATION, false);
            cache.getOriginalPreferences().getBoolean(XConstant.Key.ENABLE_VIRTUAL_WIFI, false);
            cache.getOriginalPreferences().getBoolean(XConstant.Key.ENABLE_SMART_LOCATION, false);
            cache.getOriginalPreferences().getString(XConstant.Key.LOCATION_LATITUDE, "");
            cache.getOriginalPreferences().getString(XConstant.Key.LOCATION_LONGITUDE, "");
            cache.getOriginalPreferences().getInt(XConstant.Key.LUCKY_DELAYED, 500);
        }
        
        endTime = System.currentTimeMillis();
        long originalTime = endTime - startTime;
        
        // 计算性能提升
        double improvement = ((double) (originalTime - cacheTime) / originalTime) * 100;
        
        android.util.Log.d("XPreferencesCacheTest", 
            String.format("性能测试结果: 缓存耗时=%dms, 原始耗时=%dms, 性能提升=%.1f%%", 
                cacheTime, originalTime, improvement));
    }

    /**
     * 测试缓存功能
     */
    public static void testCacheFunctionality(Context context) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        
        // 测试写入和读取
        String testKey = "test_cache_key";
        String testValue = "test_cache_value";
        
        cache.putString(testKey, testValue);
        String readValue = cache.getString(testKey, "");
        
        boolean writeReadTest = testValue.equals(readValue);
        
        // 测试缓存统计
        XPreferencesCache.CacheStats stats = cache.getCacheStats();
        
        android.util.Log.d("XPreferencesCacheTest", 
            String.format("功能测试结果: 写入读取测试=%s, 缓存统计=%s", 
                writeReadTest ? "通过" : "失败", stats.toString()));
        
        // 清理测试数据
        cache.remove(testKey);
    }

    /**
     * 运行所有测试
     */
    public static void runAllTests(Context context) {
        android.util.Log.d("XPreferencesCacheTest", "开始运行配置缓存测试...");
        
        try {
            testCacheFunctionality(context);
            testCachePerformance(context);
            android.util.Log.d("XPreferencesCacheTest", "所有测试完成");
        } catch (Exception e) {
            android.util.Log.e("XPreferencesCacheTest", "测试过程中发生异常", e);
        }
    }
}