/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.core.component;

import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.sky.xposed.annotations.APlugin;
import com.sky.xposed.common.util.Alog;
import com.sky.xposed.core.base.AbstractComponent;
import com.sky.xposed.core.info.APluginInfo;
import com.sky.xposed.core.info.LoadPackage;
import com.sky.xposed.core.interfaces.XCoreManager;
import com.sky.xposed.core.interfaces.XPlugin;
import com.sky.xposed.core.interfaces.XPluginManager;
import com.sky.xposed.core.util.FilterUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import de.robv.android.xposed.XposedHelpers;

/**
 * Created by sky on 2020-01-13.
 */
public class PluginManager extends AbstractComponent implements XPluginManager {

    // 添加优先级管理器类
    private static class PluginPriorityManager {
        private static final Map<Class<? extends XPlugin>, Integer> PRIORITIES = new HashMap<>();
        
        // 静态初始化优先级（越小优先级越高）
        static {
            // 可以根据实际需要调整各插件优先级
            try {
                // 核心功能插件（最高优先级）
                addPluginPriority("com.sky.xposed.rimet.plugin.LocationPlugin", 1);
                addPluginPriority("com.sky.xposed.rimet.plugin.SettingsPlugin", 1);
                
                // 重要但非核心插件
                addPluginPriority("com.sky.xposed.rimet.plugin.SmartLocationPlugin", 2);
                
                // 辅助功能插件
                addPluginPriority("com.sky.xposed.rimet.plugin.WifiPlugin", 3);
                addPluginPriority("com.sky.xposed.rimet.plugin.StationPlugin", 3);
                
                // 调试和特殊功能插件（最低优先级）
                addPluginPriority("com.sky.xposed.rimet.plugin.debug.DebugPlugin", 4);
            } catch (Exception e) {
                Alog.e("PluginPriorityManager", "初始化插件优先级失败", e);
            }
        }
        
        @SuppressWarnings("unchecked")
        private static void addPluginPriority(String className, int priority) {
            try {
                Class<?> clazz = Class.forName(className);
                if (XPlugin.class.isAssignableFrom(clazz)) {
                    PRIORITIES.put((Class<? extends XPlugin>) clazz, priority);
                }
            } catch (ClassNotFoundException e) {
                Alog.d("PluginPriorityManager", "插件类未找到: " + className);
            }
        }
        
        public static int getPriority(Class<? extends XPlugin> pluginClass) {
            return PRIORITIES.getOrDefault(pluginClass, 3); // 默认中等优先级
        }
    }

    private final Map<Class<? extends XPlugin>, XPlugin> mXPlugins = new HashMap<>();

    private XCoreManager mCoreManager;
    private XPluginManager.Factory mFactory;
    private XPluginManager.Loader mLoader;

    private PluginManager(Build build) {
        mCoreManager = build.mCoreManager;
        mFactory = build.mFactory;
        mLoader = build.mLoader;
    }

    @Override
    public void initialize() {
        super.initialize();
    }

    @Override
    public void release() {
        super.release();
    }

    @Override
    public XPlugin getPlugin(Class<? extends XPlugin> tClass) {
        return mXPlugins.get(tClass);
    }

    @Override
    public boolean hasPlugin(Class<? extends XPlugin> tClass) {
        return mXPlugins.containsKey(tClass);
    }

    @Override
    public List<XPlugin> getPlugins(Filter filter) {

        List<XPlugin> list = new ArrayList<>();

        for (XPlugin value : mXPlugins.values()) {
            if (filter.accept(value)) {
                list.add(value);
            }
        }
        return list;
    }

    // 重写loadPlugins方法实现真正的异步加载，集成延迟加载机制
    @Override
    public void loadPlugins() {
        // 记录开始时间用于性能统计
        final long startTime = System.currentTimeMillis();

        if (mLoader == null) {
            mLoader = new InternalPluginLoader(mCoreManager);
        }

        // 异步加载插件
        Thread pluginLoaderThread = new Thread(() -> {
            try {
                // 在后台线程加载所有插件实例，但不执行hook
                long instanceStartTime = System.currentTimeMillis();
                Map<Class<? extends XPlugin>, XPlugin> allPlugins = mLoader.loadPlugin(mFactory);
                long instanceEndTime = System.currentTimeMillis();

                // 添加到插件管理集合中
                mXPlugins.putAll(allPlugins);

                Alog.d("PluginManager", "插件实例化完成，耗时: " + (instanceEndTime - instanceStartTime)
                       + "ms，开始分类和加载插件...");

                // 分离延迟加载插件和立即加载插件
                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(() -> classifyAndLoadPlugins(allPlugins, startTime));

            } catch (Throwable t) {
                Alog.e("PluginManager", "插件加载异常", t);
            }
        });

        // 设置线程名便于调试
        pluginLoaderThread.setName("Plugin-Loader-Thread");
        pluginLoaderThread.start();
    }
    
    // 分类和加载插件（支持延迟加载）
    private void classifyAndLoadPlugins(Map<Class<? extends XPlugin>, XPlugin> allPlugins, long startTime) {
        List<XPlugin> immediatePlugins = new ArrayList<>();
        List<XPlugin> lazyPlugins = new ArrayList<>();

        // 分类插件
        for (XPlugin plugin : allPlugins.values()) {
            try {
                // 检查是否实现了LazyLoadable接口
                if (isLazyLoadablePlugin(plugin)) {
                    lazyPlugins.add(plugin);
                } else {
                    immediatePlugins.add(plugin);
                }
            } catch (Throwable e) {
                Alog.e("PluginManager", "分类插件异常: " + plugin.getClass().getSimpleName(), e);
                // 异常情况下归类为立即加载
                immediatePlugins.add(plugin);
            }
        }

        Alog.d("PluginManager", "插件分类完成 - 立即加载: " + immediatePlugins.size() +
               ", 延迟加载: " + lazyPlugins.size());

        // 立即加载关键插件
        loadImmediatePlugins(immediatePlugins, startTime);

        // 启动延迟加载
        if (!lazyPlugins.isEmpty()) {
            startLazyLoading(lazyPlugins, startTime);
        }
    }

    // 检查插件是否支持延迟加载
    private boolean isLazyLoadablePlugin(XPlugin plugin) {
        try {
            // 使用反射检查是否实现了LazyLoadable接口
            Class<?> lazyLoadableClass = Class.forName("com.sky.xposed.rimet.plugin.base.LazyLoadable");
            return lazyLoadableClass.isInstance(plugin);
        } catch (ClassNotFoundException e) {
            // LazyLoadable接口不存在，说明不支持延迟加载
            return false;
        } catch (Throwable e) {
            Alog.e("PluginManager", "检查延迟加载支持异常", e);
            return false;
        }
    }

    // 按优先级分组插件
    private Map<Integer, List<XPlugin>> groupPluginsByPriority(Map<Class<? extends XPlugin>, XPlugin> plugins) {
        Map<Integer, List<XPlugin>> groups = new HashMap<>();

        for (Map.Entry<Class<? extends XPlugin>, XPlugin> entry : plugins.entrySet()) {
            int priority = PluginPriorityManager.getPriority(entry.getKey());

            groups.computeIfAbsent(priority, k -> new ArrayList<>()).add(entry.getValue());
        }

        return groups;
    }
    
    // 立即加载关键插件
    private void loadImmediatePlugins(List<XPlugin> immediatePlugins, long startTime) {
        if (immediatePlugins.isEmpty()) {
            Alog.d("PluginManager", "没有需要立即加载的插件");
            recordPerformanceMilestone("immediate_plugins_loaded", "立即加载插件完成");
            return;
        }

        // 按优先级分组立即加载的插件
        Map<Integer, List<XPlugin>> priorityGroups = new HashMap<>();
        for (XPlugin plugin : immediatePlugins) {
            int priority = PluginPriorityManager.getPriority(plugin.getClass());
            priorityGroups.computeIfAbsent(priority, k -> new ArrayList<>()).add(plugin);
        }

        // 分阶段执行插件hook
        executePluginsHook(priorityGroups, startTime);
    }

    // 启动延迟加载
    private void startLazyLoading(List<XPlugin> lazyPlugins, long startTime) {
        try {
            // 使用反射获取LazyPluginLoader实例
            Class<?> lazyLoaderClass = Class.forName("com.sky.xposed.rimet.plugin.base.LazyPluginLoader");
            Object lazyLoader = lazyLoaderClass.getMethod("getInstance").invoke(null);

            // 添加延迟加载插件
            for (XPlugin plugin : lazyPlugins) {
                lazyLoaderClass.getMethod("addLazyPlugin", XPlugin.class).invoke(lazyLoader, plugin);
            }

            // 启动延迟加载
            lazyLoaderClass.getMethod("startLazyLoading").invoke(lazyLoader);

            Alog.d("PluginManager", "延迟加载启动完成，插件数量: " + lazyPlugins.size());

        } catch (Throwable e) {
            Alog.e("PluginManager", "启动延迟加载失败，回退到立即加载", e);
            // 回退到立即加载
            for (XPlugin plugin : lazyPlugins) {
                try {
                    plugin.hook();
                    Alog.d("PluginManager", "回退加载插件: " + plugin.getClass().getSimpleName());
                } catch (Throwable hookError) {
                    Alog.e("PluginManager", "回退加载插件失败: " + plugin.getClass().getSimpleName(), hookError);
                }
            }
        }
    }

    // 分阶段执行插件hook
    private void executePluginsHook(Map<Integer, List<XPlugin>> priorityGroups, long startTime) {
        Handler handler = new Handler(Looper.getMainLooper());

        // 优先级1（最高）：立即执行
        executePluginGroupHook(priorityGroups, 1, 0, handler);

        // 优先级2：延迟500ms
        executePluginGroupHook(priorityGroups, 2, 500, handler);

        // 优先级3：延迟1000ms
        executePluginGroupHook(priorityGroups, 3, 1000, handler);

        // 优先级4（最低）：延迟1500ms
        executePluginGroupHook(priorityGroups, 4, 1500, handler);

        // 延迟输出总耗时统计
        handler.postDelayed(() -> {
            long totalTime = System.currentTimeMillis() - startTime;
            Alog.d("PluginManager", "立即加载插件hook完成，耗时: " + totalTime + "ms");
            recordPerformanceMilestone("immediate_plugins_loaded", "立即加载插件完成");
        }, 2000);
    }
    
    // 执行指定优先级组的插件hook
    private void executePluginGroupHook(Map<Integer, List<XPlugin>> groups, 
                                       int priority, long delay, Handler handler) {
        if (!groups.containsKey(priority)) return;
        
        List<XPlugin> plugins = groups.get(priority);
        
        Runnable hookTask = () -> {
            Alog.d("PluginManager", "开始执行优先级 " + priority + " 的插件hook，数量: " + plugins.size());
            
            for (XPlugin plugin : plugins) {
                try {
                    long hookStartTime = System.currentTimeMillis();
                    plugin.hook();
                    long hookEndTime = System.currentTimeMillis();
                    
                    Alog.d("PluginManager", "插件 " + plugin.getClass().getSimpleName() 
                           + " hook耗时: " + (hookEndTime - hookStartTime) + "ms");
                } catch (Throwable t) {
                    Alog.e("PluginManager", "插件 " + plugin.getClass().getSimpleName() + " hook异常", t);
                }
            }
        };

        if (delay > 0) {
            handler.postDelayed(hookTask, delay);
        } else {
            hookTask.run();
        }
    }

    /**
     * 记录性能里程碑（使用反射避免直接依赖）
     */
    private void recordPerformanceMilestone(String key, String description) {
        try {
            Class<?> monitorClass = Class.forName("com.sky.xposed.rimet.util.PerformanceMonitor");
            Object monitor = monitorClass.getMethod("getInstance").invoke(null);
            monitorClass.getMethod("recordMilestone", String.class, String.class)
                    .invoke(monitor, key, description);
        } catch (Throwable e) {
            // 忽略性能监控异常，不影响主要功能
        }
    }

    /**
     * 设置插件加载类
     */
    private static final class InternalPluginLoader implements XPluginManager.Loader {

        private XCoreManager mCoreManager;
        private LoadPackage mLoadPackage;

        private int mVersionCode;
        private int mProcessType;

        private InternalPluginLoader(XCoreManager coreManager) {
            mCoreManager = coreManager;
            mLoadPackage = coreManager.getLoadPackage();

            mVersionCode = coreManager.getVersionManager().getVersionCode();
            mProcessType = mLoadPackage.isMainProcess() ? Process.MAIN : Process.OTHER;
        }

        @Override
        public Map<Class<? extends XPlugin>, XPlugin> loadPlugin(Factory factory) {

            List<APluginInfo> pluginInfos = getLoaderPlugins(factory);

            // 排序
            Collections.sort(pluginInfos,
                    (o1, o2) -> o1.getPriority() - o2.getPriority());

            return createPlugin(pluginInfos);
        }

        private List<APluginInfo> getLoaderPlugins(Factory factory) {

            return FilterUtil.filterList(getPluginInfos(factory.pluginList()), data -> {

                final String packageName = data.getPackageName();

                if (!TextUtils.isEmpty(packageName)
                        && !TextUtils.equals(mLoadPackage.getPackageName(), packageName)) {
                    // 包名不匹配不需要处理
                    Alog.d("包名不匹配不需要处理: " + data.getPluginClass());
                    return false;
                }

                if (!matchVersion(mVersionCode, data.getBegin(), data.getEnd())) {
                    Alog.d("版本不匹配不需要处理: " + mVersionCode  + ", "
                            + data.getBegin() + ", " + data.getEnd() + ", " + data.getPluginClass());
                    return false;
                }

                if (!matchProcess(data.getFilter(), mProcessType)) {
                    Alog.d("进程不匹配不需要处理: "
                            + mLoadPackage.getProcessName() + ", " + data.getPluginClass());
                    return false;
                }

                return true;
            });
        }

        private List<APluginInfo> getPluginInfos(List<Class<? extends XPlugin>> list) {

            List<APluginInfo> pluginInfos = new ArrayList<>();

            for (Class<? extends XPlugin> tClass: list) {

                APluginInfo info = getPluginInfo(tClass);

                if (info != null) {
                    pluginInfos.add(info);
                }
            }
            return pluginInfos;
        }

        private APluginInfo getPluginInfo(Class<? extends XPlugin> tClass) {

            APlugin plugin = tClass.getAnnotation(APlugin.class);

            return plugin != null ? new APluginInfo(
                    plugin.begin(),
                    plugin.end(),
                    plugin.filter(),
                    plugin.packageName(),
                    plugin.priority(),
                    plugin.debug(),
                    tClass)
                    : null;
        }

        private Map<Class<? extends XPlugin>, XPlugin> createPlugin(List<APluginInfo> pluginInfos) {

            Map<Class<? extends XPlugin>, XPlugin> xPluginMap = new HashMap<>();

            for (APluginInfo  info : pluginInfos) {

                XPlugin plugin = createPlugin(info);

                if (plugin != null) {
                    // 添加到表中
                    xPluginMap.put(info.getPluginClass(), plugin);
                }
            }

            return xPluginMap;
        }

        private XPlugin createPlugin(APluginInfo info) {

            try {
                // 创建插件类
                Object instance = XposedHelpers.newInstance(info.getPluginClass(), mCoreManager);
                if (instance instanceof XPlugin) {
                    XPlugin plugin = (XPlugin) instance;
                    plugin.initialize();
                    return plugin;
                } else {
                    Alog.e("创建的实例不是XPlugin类型: " + info.getPluginClass().getName());
                }
            } catch (Throwable tr) {
                Alog.e("创建插件异常", tr);
            }
            return null;
        }

        /**
         * 匹配版本
         * @param version
         * @param begin
         * @param end
         * @return
         */
        private boolean matchVersion(int version, int begin, int end) {

            if (begin == -1 && end == -1) {
                // 全版本支持
                return true;
            }

            if (end == -1) {
                // 支持>=begin的所有版本
                return version >= begin;
            } else if (begin == -1) {
                // 支持<end的所有版本
                return version < end;
            } else {
                // 支持版本之间的
                return version >= begin && version < end;
            }
        }

        /**
         * 是否匹配进程
         * @param filter
         * @param type
         * @return
         */
        private boolean matchProcess(int[] filter, int type) {

            if (filter == null || filter.length <= 0) {
                // 没有过滤不需要处理
                return true;
            }

            for (int value : filter) {
                // 匹配一个即可
                if (value == type || Process.ALL == value) {
                    return true;
                }
            }
            return false;
        }
    }

    public static class Build {

        private XCoreManager mCoreManager;
        private XPluginManager.Factory mFactory;
        private XPluginManager.Loader mLoader;

        public Build(XCoreManager coreManager) {
            mCoreManager = coreManager;
        }

        public Build setFactory(Factory factory) {
            mFactory = factory;
            return this;
        }

        public Build setLoader(Loader loader) {
            mLoader = loader;
            return this;
        }

        public XPluginManager build() {

            if (mFactory == null) {
                throw new IllegalArgumentException("参数不能为空");
            }

            return new PluginManager(this);
        }
    }
}
