---
description: Recommends a flexible project structure for Android applications, adapting to existing project organization.  # 为 Android 应用程序推荐灵活的项目结构，适应现有项目组织。
globs: app/**/*
alwaysApply: false
---
---
description: Recommends a flexible project structure for Android applications, adapting to existing project organization.
globs: app/**/*
---
# 注意：这是一个参考结构，请根据项目实际情况调整
- Note: This is a reference structure. Adapt to the project's existing organization

# 项目结构：
# Project Structure:

app/  # 应用主目录
  src/  # 源码目录
    main/  # 主代码目录
      java/com/package/  # Java/Kotlin主包路径
        data/  # 数据层，负责数据获取与存储
          repository/  # 仓库，数据来源统一接口
          datasource/  # 数据源，具体数据实现（如本地/网络）
          models/  # 数据模型
        domain/  # 领域层，业务逻辑
          usecases/  # 用例，具体业务操作
          models/  # 领域模型
          repository/  # 领域层仓库接口
        presentation/  # 表现层，界面相关
          screens/  # 各个页面
          components/  # 可复用UI组件
          theme/  # 主题与样式
          viewmodels/  # 视图模型，UI状态管理
        di/  # 依赖注入相关
        utils/  # 工具类
      res/  # 资源文件目录
        values/  # 资源值（如strings、colors、styles）
        drawable/  # 图片资源
        mipmap/  # 启动图标等
    test/  # 单元测试代码
    androidTest/  # 仪器化测试代码