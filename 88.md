# Xposed-Rimet 钉钉助手 - 详细功能介绍文档

## 1. 项目概述

**Xposed-Rimet** 是一个专门针对钉钉应用的Xposed框架插件，主要用于实现位置模拟、网络环境模拟等功能。该项目采用模块化架构设计，支持多种虚拟化功能，帮助用户在特定场景下模拟不同的设备环境和位置信息。

### 项目特点
- 🎯 **专业性**：专门针对钉钉应用优化
- 🔧 **模块化**：采用插件化架构，功能模块独立
- 🛡️ **安全性**：支持多种Xposed框架，包括LSPosed、EdXposed等
- 📱 **兼容性**：支持Android 8.0+ (API 26+)，目标SDK 35
- 🔄 **可配置**：支持配置导入导出，用户友好

## 2. 核心功能模块

### 2.1 定位模拟功能 (LocationPlugin)
- **高德地图SDK Hook**：拦截并修改高德地图SDK的定位请求
- **GPS位置伪造**：模拟任意经纬度坐标
- **位置精度控制**：支持设置位置精度范围
- **实时位置更新**：支持动态修改当前位置
- **多重定位源支持**：同时支持GPS、网络定位等多种定位方式

### 2.2 WIFI环境模拟 (WifiPlugin)
- **WIFI信息伪造**：模拟指定的WIFI网络环境
- **BSSID/SSID修改**：自定义WIFI热点信息
- **信号强度控制**：模拟不同的WIFI信号强度
- **网络状态模拟**：模拟连接/断开状态

### 2.3 基站信息模拟 (StationPlugin)
- **手机基站信息伪造**：模拟指定区域的基站信息
- **运营商信息修改**：支持修改运营商标识
- **信号强度调节**：模拟不同的信号强度
- **网络类型控制**：支持2G/3G/4G/5G网络类型模拟

### 2.4 智能定位优化 (SmartLocationPlugin)
- **性能优化**：减少Hook调用频率，提升性能
- **缓存机制**：智能缓存定位结果
- **延迟加载**：按需加载定位功能
- **内存优化**：使用WeakHashMap防止内存泄漏

### 2.5 反检测功能 (AntiDetectionPlugin)
- **框架检测对抗**：隐藏Xposed框架特征
- **应用检测绕过**：绕过钉钉的安全检测机制
- **环境伪装**：模拟正常的运行环境

## 3. 技术栈分析

### 3.1 开发语言与框架
- **主要语言**：Java (JDK 17)
- **构建工具**：Gradle 8.7.0 + Kotlin DSL
- **Android版本**：
  - 编译SDK：35 (Android 15)
  - 最低SDK：26 (Android 8.0)
  - 目标SDK：35
- **Xposed框架**：支持多种Xposed实现

### 3.2 核心依赖库
```gradle
// Xposed相关
implementation("com.github.sky-wei:xposed-javax:1.2.0")
compileOnly("de.robv.android.xposed:api:82")

// 网络请求
implementation("com.squareup.retrofit2:retrofit:2.9.0")
implementation("com.squareup.retrofit2:converter-gson:2.9.0")

// 响应式编程
implementation("io.reactivex.rxjava2:rxandroid:2.1.1")
implementation("io.reactivex.rxjava2:rxjava:2.2.21")

// 图片加载
implementation("com.squareup.picasso:picasso:2.71828")

// 崩溃收集
implementation("com.tencent.bugly:crashreport:4.1.9")

// 缓存
implementation("com.jakewharton:disklrucache:2.0.2")
```

### 3.3 地图服务集成
- **高德地图SDK**：`AMap2DMap_6.0.0_AMapSearch_9.7.4_AMapLocation_6.4.9_20241226.jar`
- **Google地图API**：备用地图服务支持
- **自定义API Key**：支持用户自定义高德地图API密钥

## 4. 项目文件结构

### 4.1 主要目录结构
```
xposed-rimet-resurrection/
├── app/                          # 主应用模块
│   ├── src/main/java/com/sky/xposed/rimet/
│   │   ├── Main.java            # Xposed模块入口点
│   │   ├── App.java             # 应用程序类
│   │   ├── XConstant.java       # 常量定义
│   │   ├── plugin/              # 功能插件目录
│   │   │   ├── LocationPlugin.java      # 定位模拟插件
│   │   │   ├── WifiPlugin.java          # WIFI模拟插件
│   │   │   ├── StationPlugin.java       # 基站模拟插件
│   │   │   ├── SmartLocationPlugin.java # 智能定位插件
│   │   │   └── AntiDetectionPlugin.java # 反检测插件
│   │   ├── ui/                  # 用户界面
│   │   │   ├── activity/        # Activity类
│   │   │   ├── adapter/         # 适配器类
│   │   │   └── dialog/          # 对话框类
│   │   ├── data/                # 数据层
│   │   │   ├── model/           # 数据模型
│   │   │   ├── service/         # 服务类
│   │   │   └── source/          # 数据源
│   │   └── util/                # 工具类
│   └── libs/                    # 第三方库文件
├── xposed-frame/                # Xposed框架模块
│   ├── annotations/             # 注解处理
│   ├── compiler/                # 编译时处理
│   ├── core/                    # 核心框架
│   └── ui/                      # UI框架
├── xposed-common/               # 公共库模块
│   └── library/                 # 公共功能库
├── docs/                        # 文档目录
├── res/                         # 资源文件
│   ├── config/                  # 配置文件
│   ├── update.json              # 更新配置
│   └── version.json             # 版本信息
└── keyStore/                    # 签名文件
    └── testkey.jks              # 测试签名
```

### 4.2 关键配置文件
- **build.gradle.kts**：项目构建配置
- **AndroidManifest.xml**：应用清单文件
- **gradle.properties**：Gradle属性配置
- **proguard-rules.pro**：代码混淆规则

## 5. 关键组件详解

### 5.1 核心入口类
- **Main.java**：Xposed模块的主入口点，负责初始化各个插件
- **App.java**：应用程序类，处理全局初始化
- **XConstant.java**：定义项目中使用的常量

### 5.2 插件基类架构
- **BaseDingPlugin**：所有插件的基类，提供通用功能
- **AbstractPresenter**：MVP架构中的Presenter基类
- **AbstractTask**：异步任务基类

### 5.3 数据管理组件
- **XPreferences**：配置数据管理，支持导入导出
- **DataException**：数据异常处理
- **M.java**：数据模型管理器

### 5.4 UI组件
- **MainActivity**：主界面Activity
- **MapActivity**：地图选择界面
- **AnalysisActivity**：自动适配界面

## 6. 配置和设置

### 6.1 Xposed模块配置
```xml
<!-- AndroidManifest.xml中的关键配置 -->
<meta-data android:name="xposedmodule" android:value="true"/>
<meta-data android:name="xposeddescription" android:value="钉钉工具"/>
<meta-data android:name="xposedminversion" android:value="54"/>
<meta-data android:name="lsposed_recommended" android:value="com.alibaba.android.rimet"/>
```

### 6.2 高德地图API配置
```xml
<meta-data
    android:name="com.amap.api.v2.apikey"
    android:value="4be0cad1a3fa0de9f41dcefe4fcb0985"/>
```

### 6.3 权限配置
- `INTERNET`：网络访问权限
- `ACCESS_FINE_LOCATION`：精确定位权限
- `ACCESS_WIFI_STATE`：WIFI状态访问权限
- `READ_PHONE_STATE`：手机状态读取权限

### 6.4 构建配置
- **签名配置**：使用testkey.jks进行签名
- **混淆配置**：Release版本启用代码混淆
- **多渠道配置**：支持demo和plugin两个渠道

## 7. 使用方式

### 7.1 环境要求
- Android 8.0+ 设备
- 已安装Xposed框架（LSPosed、EdXposed等）
- 钉钉应用

### 7.2 安装步骤
1. **下载APK**：从项目Release页面下载最新版本
2. **安装模块**：安装APK到设备
3. **激活模块**：在Xposed管理器中激活模块
4. **重启设备**：重启设备使模块生效
5. **配置功能**：打开应用进行功能配置

### 7.3 构建项目
```bash
# Windows环境
.\gradlew.bat clean assembleRelease

# Linux/Mac环境
./gradlew clean assembleRelease
```

### 7.4 自定义高德API Key
1. 注册高德开发者账号
2. 创建应用并获取API Key
3. 使用MT管理器修改APK中的API Key
4. 重新签名APK

## 8. 特殊实现细节

### 8.1 性能优化
- **延迟加载**：插件按需加载，减少启动时间
- **缓存机制**：使用多级缓存提升响应速度
- **内存管理**：使用WeakHashMap防止内存泄漏
- **Hook优化**：减少不必要的Hook调用

### 8.2 安全考虑
- **反检测机制**：隐藏框架特征，避免被检测
- **权限最小化**：只申请必要的系统权限
- **数据加密**：敏感配置数据进行加密存储

### 8.3 兼容性处理
- **多版本适配**：支持不同版本的钉钉应用
- **框架兼容**：支持多种Xposed框架实现
- **系统适配**：适配Android 8.0到15的系统版本

## 9. 注意事项

### 9.1 法律风险
- 本项目仅供学习研究使用
- 请遵守相关法律法规和公司规章制度
- 不建议在正式工作环境中使用

### 9.2 技术限制
- 部分功能可能因钉钉版本更新而失效
- 太极框架对某些功能支持有限
- 需要Root权限或支持Xposed的环境

### 9.3 使用建议
- 定期更新到最新版本
- 备份重要配置数据
- 谨慎使用在重要场合

---

**项目许可证**：Apache License 2.0  
**最后更新**：2024年12月26日  
**版本信息**：v1.4.4.3 (versionCode: 30)
