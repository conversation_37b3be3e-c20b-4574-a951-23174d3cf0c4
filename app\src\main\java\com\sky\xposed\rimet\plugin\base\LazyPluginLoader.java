/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin.base;

import android.os.Handler;
import android.os.Looper;

import com.sky.xposed.common.util.Alog;
import com.sky.xposed.core.interfaces.XPlugin;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 延迟插件加载器
 * 管理插件的延迟加载逻辑
 * 
 * Created by sky on 2024-01-01.
 */
public class LazyPluginLoader {
    
    private static final String TAG = "LazyPluginLoader";
    
    // 单例实例
    private static volatile LazyPluginLoader sInstance;
    
    // 延迟加载队列
    private final List<LazyPluginInfo> mLazyPlugins = Collections.synchronizedList(new ArrayList<>());
    
    // 已加载的插件状态跟踪
    private final ConcurrentHashMap<String, LoadState> mLoadStates = new ConcurrentHashMap<>();
    
    // 主线程Handler
    private final Handler mMainHandler = new Handler(Looper.getMainLooper());
    
    // 加载状态统计
    private final AtomicInteger mTotalPlugins = new AtomicInteger(0);
    private final AtomicInteger mLoadedPlugins = new AtomicInteger(0);
    private final AtomicBoolean mIsLoading = new AtomicBoolean(false);
    
    // 性能统计
    private long mStartTime = 0;
    
    /**
     * 插件加载状态
     */
    public enum LoadState {
        PENDING,    // 等待加载
        LOADING,    // 正在加载
        LOADED,     // 已加载
        FAILED      // 加载失败
    }
    
    /**
     * 延迟插件信息
     */
    private static class LazyPluginInfo {
        final XPlugin plugin;
        final LazyLoadable.Priority priority;
        final String name;
        final long delayMs;
        
        LazyPluginInfo(XPlugin plugin, LazyLoadable.Priority priority, String name) {
            this.plugin = plugin;
            this.priority = priority;
            this.name = name;
            this.delayMs = priority.getDelayMs();
        }
    }
    
    private LazyPluginLoader() {
        // 私有构造函数
    }
    
    /**
     * 获取单例实例
     */
    public static LazyPluginLoader getInstance() {
        if (sInstance == null) {
            synchronized (LazyPluginLoader.class) {
                if (sInstance == null) {
                    sInstance = new LazyPluginLoader();
                }
            }
        }
        return sInstance;
    }
    
    /**
     * 添加延迟加载插件
     * @param plugin 插件实例
     */
    public void addLazyPlugin(XPlugin plugin) {
        if (!(plugin instanceof LazyLoadable)) {
            Alog.w(TAG, "插件 " + plugin.getClass().getSimpleName() + " 不支持延迟加载");
            return;
        }
        
        LazyLoadable lazyPlugin = (LazyLoadable) plugin;
        if (!lazyPlugin.canLazyLoad()) {
            Alog.d(TAG, "插件 " + lazyPlugin.getPluginName() + " 不允许延迟加载，将立即加载");
            loadPluginImmediately(plugin);
            return;
        }
        
        LazyPluginInfo info = new LazyPluginInfo(plugin, lazyPlugin.getLoadPriority(), lazyPlugin.getPluginName());
        mLazyPlugins.add(info);
        mLoadStates.put(info.name, LoadState.PENDING);
        mTotalPlugins.incrementAndGet();
        
        Alog.d(TAG, "添加延迟加载插件: " + info.name + ", 优先级: " + info.priority + ", 延迟: " + info.delayMs + "ms");
    }
    
    /**
     * 开始延迟加载所有插件
     */
    public void startLazyLoading() {
        if (mIsLoading.get()) {
            Alog.w(TAG, "延迟加载已在进行中");
            return;
        }
        
        if (mLazyPlugins.isEmpty()) {
            Alog.d(TAG, "没有需要延迟加载的插件");
            return;
        }
        
        mIsLoading.set(true);
        mStartTime = System.currentTimeMillis();
        
        // 按优先级排序
        Collections.sort(mLazyPlugins, Comparator.comparingInt(info -> info.priority.getValue()));
        
        Alog.d(TAG, "开始延迟加载 " + mLazyPlugins.size() + " 个插件");
        
        // 分批加载插件
        for (LazyPluginInfo info : mLazyPlugins) {
            schedulePluginLoad(info);
        }
    }
    
    /**
     * 调度插件加载
     */
    private void schedulePluginLoad(LazyPluginInfo info) {
        mMainHandler.postDelayed(() -> {
            loadPlugin(info);
        }, info.delayMs);
    }
    
    /**
     * 加载单个插件
     */
    private void loadPlugin(LazyPluginInfo info) {
        if (mLoadStates.get(info.name) != LoadState.PENDING) {
            return;
        }
        
        mLoadStates.put(info.name, LoadState.LOADING);
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 预处理
            if (info.plugin instanceof LazyLoadable) {
                ((LazyLoadable) info.plugin).onPreLazyLoad();
            }
            
            // 执行hook
            info.plugin.hook();
            
            // 后处理
            if (info.plugin instanceof LazyLoadable) {
                ((LazyLoadable) info.plugin).onLazyLoadComplete();
            }
            
            long loadTime = System.currentTimeMillis() - startTime;
            mLoadStates.put(info.name, LoadState.LOADED);
            
            int loaded = mLoadedPlugins.incrementAndGet();
            Alog.d(TAG, "插件 " + info.name + " 加载完成, 耗时: " + loadTime + "ms (" + loaded + "/" + mTotalPlugins.get() + ")");
            
            // 检查是否全部加载完成
            if (loaded >= mTotalPlugins.get()) {
                onAllPluginsLoaded();
            }
            
        } catch (Throwable e) {
            mLoadStates.put(info.name, LoadState.FAILED);
            Alog.e(TAG, "插件 " + info.name + " 加载失败", e);
            
            // 即使失败也要检查是否完成
            int loaded = mLoadedPlugins.incrementAndGet();
            if (loaded >= mTotalPlugins.get()) {
                onAllPluginsLoaded();
            }
        }
    }
    
    /**
     * 立即加载插件
     */
    private void loadPluginImmediately(XPlugin plugin) {
        try {
            plugin.hook();
            Alog.d(TAG, "插件 " + plugin.getClass().getSimpleName() + " 立即加载完成");
        } catch (Throwable e) {
            Alog.e(TAG, "插件 " + plugin.getClass().getSimpleName() + " 立即加载失败", e);
        }
    }
    
    /**
     * 所有插件加载完成
     */
    private void onAllPluginsLoaded() {
        mIsLoading.set(false);
        long totalTime = System.currentTimeMillis() - mStartTime;
        
        int loaded = 0;
        int failed = 0;
        for (LoadState state : mLoadStates.values()) {
            if (state == LoadState.LOADED) {
                loaded++;
            } else if (state == LoadState.FAILED) {
                failed++;
            }
        }
        
        Alog.d(TAG, "所有延迟加载插件处理完成! 总耗时: " + totalTime + "ms, 成功: " + loaded + ", 失败: " + failed);
    }
    
    /**
     * 获取插件加载状态
     */
    public LoadState getPluginState(String pluginName) {
        return mLoadStates.getOrDefault(pluginName, LoadState.PENDING);
    }
    
    /**
     * 是否正在加载
     */
    public boolean isLoading() {
        return mIsLoading.get();
    }
    
    /**
     * 获取加载进度
     */
    public float getLoadProgress() {
        int total = mTotalPlugins.get();
        if (total == 0) return 1.0f;
        return (float) mLoadedPlugins.get() / total;
    }
}
