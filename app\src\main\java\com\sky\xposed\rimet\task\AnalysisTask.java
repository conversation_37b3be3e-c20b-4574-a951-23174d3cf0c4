/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.task;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;

import com.sky.xposed.common.util.Alog;
import com.sky.xposed.common.util.ClassUtil;
import com.sky.xposed.rimet.XConstant;
import com.sky.xposed.rimet.data.M;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;

import dalvik.system.DexFile;
import dalvik.system.PathClassLoader;

/**
 * Created by sky on 2020-03-22.
 */
public class AnalysisTask extends AbstractTask<String, String, Map<Integer, String>> {

    @SuppressLint("StaticFieldLeak")
    private Context mContext;
    private ClassLoader mClassLoader;
    private Class mDatabaseClass;

    private OnPreCallback mOnPreCallback;
    private OnProgressCallback mOnProgressCallback;

    private Map<Integer, String> mStringMap = new HashMap<>();

    public AnalysisTask(Context context) {
        mContext = context;
    }

    public OnPreCallback getOnPreCallback() {
        return mOnPreCallback;
    }

    public void setOnPreCallback(OnPreCallback onPreCallback) {
        mOnPreCallback = onPreCallback;
    }

    public OnProgressCallback getOnProgressCallback() {
        return mOnProgressCallback;
    }

    public void setOnProgressCallback(OnProgressCallback onProgressCallback) {
        mOnProgressCallback = onProgressCallback;
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();
        onProgressUpdate("准备开始分析...");
        if (mOnPreCallback != null) mOnPreCallback.onPreExecute();
    }

    @Override
    protected void onProgressUpdate(String... values) {
        super.onProgressUpdate(values);
        if (mOnProgressCallback != null) mOnProgressCallback.onProgressUpdate(values[0]);
    }

    @Override
    protected Map<Integer, String> onHandler(String... strings) throws Exception {

        if (strings == null || strings.length != 1 || TextUtils.isEmpty(strings[0])) {
            publishProgress("参数异常!");
            return null;
        }
        String packageName = strings[0];
        String sourcePath = getSourcePath(packageName);

        long startTime = System.currentTimeMillis();

        List<String> classNames = getDefaultClassName(sourcePath);
        mClassLoader = new PathClassLoader(sourcePath, ClassLoader.getSystemClassLoader());

        mDatabaseClass = loadClass("com.alibaba.wukong.im.base.IMDatabase");
        Alog.d("current analisis package:", packageName);
        for (String className : classNames) {

            handlerClass(className, packageName);

            // 只需要找到ServiceFactory类即可退出
            if (mStringMap.containsKey(M.classz.class_defpackage_ServiceFactory)) {
                // 不需要处理了
                Alog.d(">>>>>>>>>>>>>>> 已找到ServiceFactory，不需要继续处理");
                break;
            }
        }

        publishProgress("分析完成!");
        Alog.d(">>>>>>>>>>>>>>>>>> " + (System.currentTimeMillis() - startTime));
        publishProgress("总共耗时:" + (System.currentTimeMillis() - startTime) + "毫秒");
        return mStringMap;
    }

    private String getSourcePath(String packageName) throws PackageManager.NameNotFoundException {
        ApplicationInfo info = mContext.getPackageManager().getApplicationInfo(packageName, 0);
        
        // 为Android 15的动态代码加载安全要求，确保文件是只读的
        File sourceFile = new File(info.sourceDir);
        if (sourceFile.exists() && !sourceFile.setReadOnly()) {
            // 记录警告但继续执行
            Alog.w(this.getClass().getName(), "无法将源文件设置为只读: " + sourceFile.getAbsolutePath());
        }
        
        return info.sourceDir;
    }

    private void handlerClass(String className, String packageName) {

        Class tClass = loadClass(className);

        if (tClass == null) return;

        int modifier = tClass.getModifiers();

        if (!Modifier.isPublic(modifier) || !Modifier.isFinal(modifier)
                || tClass.isInterface() || tClass.isAnnotation() || tClass.isEnum()) {
            // 不需要处理
            return;
        }

        publishProgress("正在分析:" + tClass.getName() + "\n");

        Class superClass = tClass.getSuperclass();

        if (Object.class.equals(superClass) && handlerServiceFactoryClass(tClass)) {
            Alog.d(">>>>>>>>>>>>>>>>>>>> ServiceFactory " + tClass);
            mStringMap.put(M.classz.class_defpackage_ServiceFactory, tClass.getName());
            Alog.d("ServiceFactory保存", "类名已保存到mStringMap: " + tClass.getName());
        }
        // 注释掉不需要的红包和消息处理类的检测
        /*
        else if (Object.class.equals(superClass) && !packageName.equals(XConstant.Rimet.PACKAGE_NAME.get(1)) && handlerRedPacketsClass(tClass)) {
            Alog.d(">>>>>>>>>>>>>>>>>>>> RedPackets " + tClass);
            mStringMap.put(M.classz.class_defpackage_RedPacketsRpc, tClass.getName());
        } else if (superClass == mDatabaseClass && handlerDatabaseClass(tClass)) {
            if (mStringMap.containsKey(M.classz.class_defpackage_MessageDs))
                return;//防止扫描到MessageDs2
            Alog.d(">>>>>>>>>>>>>>>>>>>> Database " + tClass);
            mStringMap.put(M.classz.class_defpackage_MessageDs, tClass.getName());
        }
        */
    }

    private Class loadClass(String className) {
        try {
            return mClassLoader.loadClass(className);
        } catch (ClassNotFoundException ignored) {
        }
        return null;
    }

    private boolean handlerDatabaseClass(Class tClass) {

        try {
            Method method = ClassUtil.findMethod(tClass, value -> {
                Class[] classes = value.getParameterTypes();
                return Map.class.equals(value.getReturnType()) && classes.length == 3;
            });
            return method != null;
        } catch (Throwable ignored) {
        }
        return false;
    }

    private boolean handlerServiceFactoryClass(Class tClass) {
        try {
            Field[] fields = tClass.getDeclaredFields();
            Method[] methods = tClass.getDeclaredMethods();

            // 添加更详细的日志，帮助定位问题
            Alog.d("ServiceFactory判断", "类名: " + tClass.getName() + 
                   ", fields数量: " + fields.length + 
                   ", methods数量: " + methods.length);
            
            // 进一步放宽条件
            if (methods.length >= 1) {
                // 检查类中是否有名为"a"的方法
                boolean hasMethodA = false;
                for (Method method : methods) {
                    if ("a".equals(method.getName())) {
                        hasMethodA = true;
                        Alog.d("ServiceFactory判断", "找到名为'a'的方法");
                        break;
                    }
                }
                
                // 如果有字段是ConcurrentMap类型，或者有名为"a"的方法，都认为可能是ServiceFactory
                if ((fields.length > 0 && fields[0] != null && ConcurrentMap.class.equals(fields[0].getType())) || hasMethodA) {
                    // 只检查是否有接收Class参数的方法
                    for (Method method : methods) {
                        if (method.getParameterTypes().length == 1 
                           && method.getParameterTypes()[0].equals(Class.class)) {
                            Alog.d("ServiceFactory判断", "找到符合条件的方法: " + method.getName());
                            return true;
                        }
                    }
                }
            }
            return false;
        } catch (Throwable tr) {
            Alog.e("ServiceFactory判断异常", tr);
        }
        return false;
    }

    private boolean handlerRedPacketsClass(Class tClass) {
        try {
            Field[] fields = tClass.getDeclaredFields();

            if (fields.length == 1
                    && tClass.equals(fields[0].getType())
                    && tClass.getDeclaredMethods().length > 10) {
                // 获取方法参数
                Method method = ClassUtil.findMethod(
                        tClass, value -> value.getParameterTypes().length > 13);
                return method != null;
            }
        } catch (Throwable ignored) {
        }
        return false;
    }

    private Method getMethod(Class<?> tClass, String name, Class<?>... parameterTypes) {
        try {
            return tClass.getDeclaredMethod(name, parameterTypes);
        } catch (Throwable ignored) {
        }
        return null;
    }

    /**
     * 获取默认的包
     *
     * @param sourcePath
     * @return
     * @throws IOException
     */
    private List<String> getDefaultClassName(String sourcePath) throws IOException {
        // 为Android 15的动态代码加载安全要求，确保文件是只读的
        File sourceFile = new File(sourcePath);
        if (sourceFile.exists() && !sourceFile.setReadOnly()) {
            // 记录警告但继续执行
            Alog.w(this.getClass().getName(), "无法将源文件设置为只读: " + sourceFile.getAbsolutePath());
        }

        DexFile dexFile = new DexFile(sourcePath);
        Enumeration<String> enumeration = dexFile.entries();

        List<String> classNames = new ArrayList<>();

        while (enumeration.hasMoreElements()) {

            String className = enumeration.nextElement();

            if (!className.contains(".") && !className.contains("$")) {
                // 默认包
                classNames.add(className);
            }
        }
        return classNames;
    }

    public interface OnPreCallback {

        void onPreExecute();
    }

    public interface OnProgressCallback {

        void onProgressUpdate(String text);
    }
}
