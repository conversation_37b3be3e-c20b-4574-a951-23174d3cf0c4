# Android 15 (API 35) 适配清单

## 已完成任务
- [x] 更新应用targetSdk到35（已在SDK_UPGRADE.md中记录）
- [x] 更新依赖库版本（已在SDK_UPGRADE.md中记录）

## 待完成任务

### 权限相关
- [x] 优化存储权限处理 - 更新READ_EXTERNAL_STORAGE和WRITE_EXTERNAL_STORAGE权限（使用maxSdkVersion属性限制）
- [x] 确保READ_MEDIA_IMAGES权限使用正确

### 边到边显示适配
- [x] 检查Activity是否支持边到边显示
- [x] 为MainActivity添加适当的WindowInsets处理（通过setDecorFitsSystemWindows(true)保持传统布局）
- [x] 为MapActivity添加适当的WindowInsets处理（通过setDecorFitsSystemWindows(true)保持传统布局）
- [x] 为AnalysisActivity添加适当的WindowInsets处理（通过setDecorFitsSystemWindows(true)保持传统布局）

### OpenJDK 17兼容性
- [x] 检查格式化字符串API使用（String.format和Formatter.format）
- [x] 检查UUID.fromString()调用和验证
- [x] 检查Arrays.asList().toArray()类型转换使用

### 安全增强
- [x] 检查Intent使用的安全性（确保Intent有明确的操作）
- [x] 确保PendingIntent安全使用（未发现PendingIntent的使用）
- [x] 检查是否使用动态代码加载（DCL）并确保文件为只读

### TLS安全性
- [x] 确保不使用或已弃用TLS 1.0/1.1


### 应用停止状态处理
- [ ] 添加处理应用被强制停止后状态恢复的逻辑

### 16KB页面大小支持
- [x] 确保原生库（.so文件）兼容16KB页面大小

## 测试验证
- [ ] 在Android 15模拟器上测试应用基本功能
- [ ] 验证权限请求和授权流程
- [ ] 测试UI在不同设备和屏幕尺寸上的边到边显示
- [ ] 测试应用在被强制停止后的恢复行为

## 注意事项
- 默认情况下，Android 15上的应用会使用边到边显示模式，需要正确处理系统栏
- 需要确保所有Intent都有明确的操作，否则可能导致与Intent过滤器不匹配
- OepnJDK 17对字符串格式化API有更严格的检查，需要确保格式正确 