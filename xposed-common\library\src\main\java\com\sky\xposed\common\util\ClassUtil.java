/*
 * Copyright (c) 2018 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.common.util;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * 类相关工具类
 */
public class ClassUtil {

    private static final String TAG = "ClassUtil";

    private ClassUtil() {
    }

    /**
     * 获取类名
     * @param className 完整类名
     * @return 类名
     */
    public static String getSimpleName(String className) {
        if (className == null) return "";
        int lastDot = className.lastIndexOf('.');
        return lastDot >= 0 ? className.substring(lastDot + 1) : className;
    }

    /**
     * 获取类实例
     * @param className 类名
     * @return 类实例
     */
    public static Class<?> forName(String className) {
        try {
            return Class.forName(className);
        } catch (Throwable tr) {
            Alog.e(TAG, "获取类异常: " + className, tr);
            return null;
        }
    }

    /**
     * 获取构造函数
     * @param clazz 类
     * @param parameterTypes 参数类型
     * @return 构造函数
     */
    public static Constructor<?> getConstructor(Class<?> clazz, Class<?>... parameterTypes) {
        try {
            return clazz.getConstructor(parameterTypes);
        } catch (Throwable tr) {
            Alog.e(TAG, "获取构造函数异常: " + clazz.getName() + 
                   ", parameterTypes: " + Arrays.toString(parameterTypes), tr);
            return null;
        }
    }

    /**
     * 获取方法
     * @param clazz 类
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @return 方法
     */
    public static Method getMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        try {
            return clazz.getMethod(methodName, parameterTypes);
        } catch (Throwable tr) {
            Alog.e(TAG, "获取方法异常: " + clazz.getName() + 
                   ", methodName: " + methodName + 
                   ", parameterTypes: " + Arrays.toString(parameterTypes), tr);
            return null;
        }
    }

    /**
     * 获取声明的方法（包括私有方法）
     * @param clazz 类
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @return 方法
     */
    public static Method getDeclaredMethod(Class<?> clazz, String methodName, Class<?>... parameterTypes) {
        try {
            Method method = clazz.getDeclaredMethod(methodName, parameterTypes);
            method.setAccessible(true);
            return method;
        } catch (Throwable tr) {
            Alog.e(TAG, "获取声明方法异常: " + clazz.getName() + 
                   ", methodName: " + methodName + 
                   ", parameterTypes: " + Arrays.toString(parameterTypes), tr);
            return null;
        }
    }

    /**
     * 获取字段
     * @param clazz 类
     * @param fieldName 字段名
     * @return 字段
     */
    public static Field getField(Class<?> clazz, String fieldName) {
        try {
            return clazz.getField(fieldName);
        } catch (Throwable tr) {
            Alog.e(TAG, "获取字段异常: " + clazz.getName() + 
                   ", fieldName: " + fieldName, tr);
            return null;
        }
    }

    /**
     * 获取声明的字段（包括私有字段）
     * @param clazz 类
     * @param fieldName 字段名
     * @return 字段
     */
    public static Field getDeclaredField(Class<?> clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field;
        } catch (Throwable tr) {
            Alog.e(TAG, "获取声明字段异常: " + clazz.getName() + 
                   ", fieldName: " + fieldName, tr);
            return null;
        }
    }

    /**
     * 创建实例
     * @param className 类名
     * @return 实例对象
     */
    public static Object newInstance(String className) {
        try {
            Class<?> clazz = Class.forName(className);
            return clazz.newInstance();
        } catch (Throwable tr) {
            Alog.e(TAG, "创建实例异常: " + className, tr);
            return null;
        }
    }

    /**
     * 调用静态方法
     * @param clazz 类
     * @param methodName 方法名
     * @param parameterTypes 参数类型
     * @param args 参数
     * @return 返回值
     */
    public static Object invokeStaticMethod(Class<?> clazz, String methodName, 
                                         Class<?>[] parameterTypes, Object[] args) {
        try {
            Method method = clazz.getMethod(methodName, parameterTypes);
            return method.invoke(null, args);
        } catch (Throwable tr) {
            Alog.e(TAG, "调用静态方法异常: " + clazz.getName() + 
                   ", methodName: " + methodName, tr);
            return null;
        }
    }

    /**
     * 接口：方法过滤器
     */
    public interface MethodFilter {
        /**
         * 判断方法是否匹配条件
         * @param method 方法
         * @return 是否匹配
         */
        boolean accept(Method method);
    }
    
    /**
     * 查找符合条件的方法
     * @param clazz 类
     * @param filter 方法过滤器
     * @return 匹配的方法，如果没有找到则返回null
     */
    public static Method findMethod(Class<?> clazz, MethodFilter filter) {
        if (clazz == null || filter == null) return null;
        
        try {
            // 获取所有声明的方法
            Method[] methods = clazz.getDeclaredMethods();
            
            for (Method method : methods) {
                if (filter.accept(method)) {
                    method.setAccessible(true);
                    return method;
                }
            }
            
            // 递归查找父类的方法
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && superClass != Object.class) {
                return findMethod(superClass, filter);
            }
        } catch (Throwable tr) {
            Alog.e(TAG, "查找方法异常: " + clazz.getName(), tr);
        }
        
        return null;
    }
} 