plugins {
    id("java-library")
    id("maven-publish")
}

dependencies {
    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

afterEvaluate {
    publishing {
        publications {
            create<MavenPublication>("release") {
                from(components["java"])
                groupId = "com.github.jingcai-wei.xposed-frame"
                artifactId = "annotations"
                version = "1.1.1"
            }
        }
    }
} 