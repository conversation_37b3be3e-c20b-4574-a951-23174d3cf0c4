/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin.base;

import android.text.TextUtils;

import com.sky.xposed.core.base.AbstractPlugin;
import com.sky.xposed.core.interfaces.XCoreManager;
import com.sky.xposed.rimet.XConstant;
import com.sky.xposed.rimet.data.M;
import com.sky.xposed.rimet.data.cache.XPreferencesCache;
import com.sky.xposed.rimet.data.cache.XPreferencesCacheFactory;

/**
 * Created by sky on 2020-03-24.
 */
public abstract class BaseDingPlugin extends AbstractPlugin {

    private XPreferencesCache mPreferencesCache;

    public BaseDingPlugin(XCoreManager coreManager) {
        super(coreManager);
    }

    @Override
    public void initialize() {
        super.initialize();
        // 初始化配置缓存
        try {
            mPreferencesCache = XPreferencesCacheFactory.getCache(getCoreManager().getResourceManager().getHookContext());
        } catch (Exception e) {
            // 如果缓存初始化失败，记录日志但继续使用原有方式
            android.util.Log.w("BaseDingPlugin", "配置缓存初始化失败，将使用原有配置读取方式", e);
        }
    }

    protected boolean isOpenHook() {
        return TextUtils.equals(getPString(XConstant.Key.PACKAGE_MD5), getXString(M.sky.rimet_package_md5));
    }

    @Override
    public String getXString(int key) {
        final String value = super.getXString(key);
        return TextUtils.isEmpty(value) ? getAString(key) : value;
    }

    /**
     * 获取分析出来的
     * @param key
     * @return
     */
    private String getAString(int key) {
        return getPString(Integer.toHexString(key), "");
    }

    /**
     * 保存布尔值到首选项（同时更新缓存）
     * @param key 键
     * @param value 值
     */
    protected void putPBoolean(String key, boolean value) {
        if (mPreferencesCache != null) {
            mPreferencesCache.putBoolean(key, value);
        } else {
            getPreferences().putBoolean(key, value);
        }
    }

    /**
     * 保存整数值到首选项（同时更新缓存）
     * @param key 键
     * @param value 值
     */
    protected void putPInt(String key, int value) {
        if (mPreferencesCache != null) {
            mPreferencesCache.putInt(key, value);
        } else {
            getPreferences().putInt(key, value);
        }
    }

    /**
     * 保存字符串值到首选项（同时更新缓存）
     * @param key 键
     * @param value 值
     */
    protected void putPString(String key, String value) {
        if (mPreferencesCache != null) {
            mPreferencesCache.putString(key, value);
        } else {
            getPreferences().putString(key, value);
        }
    }

    /**
     * 保存长整数值到首选项（同时更新缓存）
     * @param key 键
     * @param value 值
     */
    protected void putPLong(String key, long value) {
        if (mPreferencesCache != null) {
            mPreferencesCache.putLong(key, value);
        } else {
            getPreferences().putLong(key, value);
        }
    }

    /**
     * 保存浮点数值到首选项（同时更新缓存）
     * @param key 键
     * @param value 值
     */
    protected void putPFloat(String key, float value) {
        if (mPreferencesCache != null) {
            mPreferencesCache.putFloat(key, value);
        } else {
            getPreferences().putFloat(key, value);
        }
    }

    /**
     * 从首选项获取布尔值（优先使用缓存）
     * @param key 键
     * @param defValue 默认值
     * @return 布尔值
     */
    protected boolean getPBoolean(String key, boolean defValue) {
        if (mPreferencesCache != null) {
            return mPreferencesCache.getBoolean(key, defValue);
        } else {
            return getPreferences().getBoolean(key, defValue);
        }
    }

    /**
     * 从首选项获取整数值（优先使用缓存）
     * @param key 键
     * @param defValue 默认值
     * @return 整数值
     */
    public int getPInt(String key, int defValue) {
        if (mPreferencesCache != null) {
            return mPreferencesCache.getInt(key, defValue);
        } else {
            return super.getPInt(key, defValue);
        }
    }

    /**
     * 从首选项获取字符串值（优先使用缓存）
     * @param key 键
     * @param defValue 默认值
     * @return 字符串值
     */
    public String getPString(String key, String defValue) {
        if (mPreferencesCache != null) {
            return mPreferencesCache.getString(key, defValue);
        } else {
            return super.getPString(key, defValue);
        }
    }

    /**
     * 从首选项获取长整数值（优先使用缓存）
     * @param key 键
     * @param defValue 默认值
     * @return 长整数值
     */
    public long getPLong(String key, long defValue) {
        if (mPreferencesCache != null) {
            return mPreferencesCache.getLong(key, defValue);
        } else {
            return super.getPLong(key, defValue);
        }
    }

    /**
     * 从首选项获取浮点数值（优先使用缓存）
     * @param key 键
     * @param defValue 默认值
     * @return 浮点数值
     */
    public float getPFloat(String key, float defValue) {
        if (mPreferencesCache != null) {
            return mPreferencesCache.getFloat(key, defValue);
        } else {
            return super.getPFloat(key, defValue);
        }
    }

    /**
     * 获取配置缓存实例（用于高级操作）
     * @return 配置缓存实例，可能为null
     */
    protected XPreferencesCache getPreferencesCache() {
        return mPreferencesCache;
    }
}
