---
description: Enforces specific UI-related guidelines for Jetpack Compose within the presentation layer.  # 在表示层中强制执行 Jetpack Compose 的特定 UI 相关指南。
globs: app/src/main/java/com/package/presentation/**/*.kt
alwaysApply: false
---
---
description: Enforces specific UI-related guidelines for Jetpack Compose within the presentation layer.
globs: app/src/main/java/com/package/presentation/**/*.kt
---
# 合理使用remember和derivedStateOf
- Use remember and derivedStateOf appropriately.
# 实现合适的重组优化
- Implement proper recomposition optimization.
# 合理排序Compose修饰符
- Use proper Compose modifiers ordering.
# 遵循可组合函数命名规范
- Follow composable function naming conventions.
# 实现合适的预览注解
- Implement proper preview annotations.
# 使用MutableState实现合适的状态管理
- Use proper state management with MutableState.
# 实现合适的错误处理和加载状态
- Implement proper error handling and loading states.
# 使用MaterialTheme实现合适的主题
- Use proper theming with MaterialTheme.
# 遵循无障碍准则
- Follow accessibility guidelines.
# 实现合适的动画模式
- Implement proper animation patterns.