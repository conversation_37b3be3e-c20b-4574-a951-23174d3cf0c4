/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.ui.dialog;

import android.os.Bundle;
import android.view.Gravity;
import android.view.Menu;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.sky.xposed.common.util.ToastUtil;
import com.sky.xposed.rimet.R;
import com.sky.xposed.rimet.util.IconUtil;
import com.sky.xposed.ui.util.DisplayUtil;
import com.sky.xposed.ui.util.LayoutUtil;
import com.sky.xposed.ui.util.ViewUtil;
import com.sky.xposed.ui.view.PluginFrameLayout;

/**
 * 图标演示对话框，展示所有图标
 */
public class IconDemoDialog extends RimetPluginDialog {

    @Override
    public void createView(PluginFrameLayout frameView) {
        
        setTitle("图标演示");
        
        // 显示返回按钮
        showBack();
        
        // 显示更多菜单
        showMoreMenu();
        
        // 添加更多自定义按钮
        addMoreImageView(R.drawable.ic_action_check, v -> ToastUtil.show("点击了确认按钮"));
        
        // 创建图标展示区域
        LinearLayout iconLayout = new LinearLayout(getContext());
        iconLayout.setOrientation(LinearLayout.VERTICAL);
        iconLayout.setPadding(DisplayUtil.DIP_15, DisplayUtil.DIP_15, DisplayUtil.DIP_15, DisplayUtil.DIP_15);
        
        // 添加所有图标的展示
        addIconDemo(iconLayout, "返回图标", view -> IconUtil.setBackIcon((ImageView)view));
        addIconDemo(iconLayout, "确认图标", view -> IconUtil.setCheckIcon((ImageView)view));
        addIconDemo(iconLayout, "清除图标", view -> IconUtil.setClearIcon((ImageView)view));
        addIconDemo(iconLayout, "更多选项图标", view -> IconUtil.setMoreVertIcon((ImageView)view));
        addIconDemo(iconLayout, "右箭头图标", view -> IconUtil.setRightArrowIcon((ImageView)view));
        
        // 添加到对话框内容区域
        frameView.addSubView(iconLayout);
    }
    
    private void addIconDemo(LinearLayout parent, String title, IconSetter iconSetter) {
        // 创建标题
        TextView textView = new TextView(getContext());
        textView.setText(title);
        textView.setTextSize(16);
        textView.setPadding(0, DisplayUtil.DIP_10, 0, DisplayUtil.DIP_5);
        parent.addView(textView);
        
        // 创建图标容器
        LinearLayout iconContainer = new LinearLayout(getContext());
        iconContainer.setOrientation(LinearLayout.HORIZONTAL);
        iconContainer.setGravity(Gravity.CENTER_VERTICAL);
        
        // 创建图标背景
        View background = new View(getContext());
        background.setBackgroundColor(0xFF424242); // 暗灰色背景
        LinearLayout.LayoutParams bgParams = LayoutUtil.newLinearLayoutParams(DisplayUtil.DIP_40, DisplayUtil.DIP_40);
        iconContainer.addView(background, bgParams);
        
        // 创建图标
        ImageView imageView = new ImageView(getContext());
        LinearLayout.LayoutParams iconParams = LayoutUtil.newLinearLayoutParams(DisplayUtil.DIP_24, DisplayUtil.DIP_24);
        iconParams.leftMargin = -DisplayUtil.DIP_30; // 覆盖在背景上
        iconContainer.addView(imageView, iconParams);
        
        // 设置图标
        iconSetter.setIcon(imageView);
        
        parent.addView(iconContainer);
    }
    
    @Override
    public void onCreateMoreMenu(Menu menu) {
        menu.add(1, 1, 1, "选项1");
        menu.add(1, 2, 2, "选项2");
        menu.add(1, 3, 3, "选项3");
    }
    
    interface IconSetter {
        void setIcon(ImageView view);
    }
} 