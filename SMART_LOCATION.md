# 智能虚拟定位功能实现

智能虚拟定位功能能够根据时间自动切换虚拟定位状态，满足上下班打卡需求。当处于工作时间时，虚拟定位会自动关闭，使用真实地理位置打卡；当处于非工作时间时，虚拟定位自动开启，使用虚拟地理位置打卡。

## 完成的任务

- [x] 添加智能虚拟定位相关的配置常量 (XConstant.java)
- [x] 创建智能虚拟定位插件类 (SmartLocationPlugin.java)
- [x] 在SettingsDialog中添加智能虚拟定位相关的设置界面
- [x] 实现应用前后台状态检测
- [x] 实现基于时间的自动虚拟定位切换
- [x] 实现工作时间自定义配置
- [x] 修复钉钉启动时立即进行虚拟定位状态检查
- [x] 更新上班时间默认设置为07:00（原为09:00）
- [x] 改进时间范围检测逻辑，支持跨天时间设置
- [x] 移除后台检测功能，只在前台运行

## 进行中的任务

- [ ] 测试智能虚拟定位功能稳定性，并完善错误处理
- [ ] 添加状态通知，提示用户当前虚拟定位状态

## 未来任务

- [ ] 添加更灵活的时间规则配置，如工作日/休息日不同配置
- [ ] 添加位置切换的通知提示
- [ ] 添加手动切换按钮，用于临时覆盖自动规则

## 使用说明

1. 打开钉钉助手设置界面
2. 在"打卡"部分找到"智能虚拟定位"选项，开启此功能
3. 设置上班时间和下班时间，默认为07:00-18:00
4. 确认虚拟定位功能已正确配置，包括设置正确的位置信息

## 最新变更说明 (2025/04/25)

1. 修复了钉钉启动后不立即进行时间检查的问题
   - 现在程序启动时立即检查时间并设置正确的虚拟定位状态
   - 前台切换时也会立即强制检查一次虚拟定位状态

2. 移除了后台检测功能
   - 功能现在只在应用前台时运行
   - 减少电量消耗和系统资源占用
   - 简化了用户界面和配置选项

3. 更新了默认时间设置
   - 上班时间默认设置为07:00（原为09:00）
   - 优化了时间检测逻辑，支持跨天时间设置

4. 增加了详细的调试日志
   - 每次检查时间都会记录详细信息
   - 便于排查时间检测和虚拟定位切换问题

## 工作原理

智能虚拟定位功能按照以下机制工作：

1. 在钉钉应用启动和前台活动时，立即检查当前时间
2. 根据配置的时间规则决定是否开启或关闭虚拟定位：
   - 上班时间段内：关闭虚拟定位，使用真实位置打卡
   - 下班时间段内：开启虚拟定位，使用虚拟位置打卡
3. 当钉钉处于后台时，停止检测以节省电量
4. 应用前台时每分钟检查一次时间

## 错误处理

该功能包含多重错误处理机制，确保即使在出现异常的情况下也不会影响钉钉应用的正常运行：

1. 所有关键操作都有try-catch保护
2. 时间格式解析错误处理
3. 应用生命周期监听的容错处理
4. 组件初始化失败的处理

## 相关文件

- app/src/main/java/com/sky/xposed/rimet/XConstant.java - 配置常量定义
- app/src/main/java/com/sky/xposed/rimet/plugin/SmartLocationPlugin.java - 功能实现
- app/src/main/java/com/sky/xposed/rimet/ui/dialog/SettingsDialog.java - 设置界面 