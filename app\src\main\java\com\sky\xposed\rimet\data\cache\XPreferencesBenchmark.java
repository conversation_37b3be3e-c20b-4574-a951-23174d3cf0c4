/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.data.cache;

import android.content.Context;

import com.sky.xposed.rimet.XConstant;

/**
 * XPreferences性能基准测试
 * 用于对比缓存和原始SharedPreferences的性能差异
 */
public class XPreferencesBenchmark {

    private static final int BENCHMARK_ITERATIONS = 10000;
    
    // 测试用的配置键
    private static final String[] TEST_KEYS = {
        XConstant.Key.ENABLE_VIRTUAL_LOCATION,
        XConstant.Key.ENABLE_VIRTUAL_WIFI,
        XConstant.Key.ENABLE_SMART_LOCATION,
        XConstant.Key.LOCATION_LATITUDE,
        XConstant.Key.LOCATION_LONGITUDE,
        XConstant.Key.LUCKY_DELAYED,
        XConstant.Key.WIFI_SS_ID,
        XConstant.Key.STATION_MCC
    };

    /**
     * 运行完整的性能基准测试
     */
    public static BenchmarkResult runBenchmark(Context context) {
        XPreferencesCache cache = XPreferencesCacheFactory.getCache(context);
        
        // 预热缓存
        warmupCache(cache);
        
        // 测试缓存性能
        long cacheTime = benchmarkCacheReads(cache);
        
        // 测试原始SharedPreferences性能
        long originalTime = benchmarkOriginalReads(cache);
        
        // 测试批量读取性能
        long batchTime = benchmarkBatchReads(cache);
        
        return new BenchmarkResult(cacheTime, originalTime, batchTime, BENCHMARK_ITERATIONS);
    }

    /**
     * 预热缓存
     */
    private static void warmupCache(XPreferencesCache cache) {
        // 预加载测试键
        cache.preloadKeys(TEST_KEYS);
        
        // 执行一些读取操作来预热
        for (int i = 0; i < 100; i++) {
            for (String key : TEST_KEYS) {
                cache.getString(key, "");
                cache.getBoolean(key, false);
                cache.getInt(key, 0);
            }
        }
    }

    /**
     * 测试缓存读取性能
     */
    private static long benchmarkCacheReads(XPreferencesCache cache) {
        long startTime = System.nanoTime();
        
        for (int i = 0; i < BENCHMARK_ITERATIONS; i++) {
            for (String key : TEST_KEYS) {
                cache.getBoolean(key, false);
                cache.getString(key, "");
                cache.getInt(key, 0);
            }
        }
        
        long endTime = System.nanoTime();
        return (endTime - startTime) / 1_000_000; // 转换为毫秒
    }

    /**
     * 测试原始SharedPreferences读取性能
     */
    private static long benchmarkOriginalReads(XPreferencesCache cache) {
        long startTime = System.nanoTime();
        
        for (int i = 0; i < BENCHMARK_ITERATIONS; i++) {
            for (String key : TEST_KEYS) {
                cache.getOriginalPreferences().getBoolean(key, false);
                cache.getOriginalPreferences().getString(key, "");
                cache.getOriginalPreferences().getInt(key, 0);
            }
        }
        
        long endTime = System.nanoTime();
        return (endTime - startTime) / 1_000_000; // 转换为毫秒
    }

    /**
     * 测试批量读取性能
     */
    private static long benchmarkBatchReads(XPreferencesCache cache) {
        long startTime = System.nanoTime();
        
        for (int i = 0; i < BENCHMARK_ITERATIONS / 10; i++) { // 批量读取次数较少
            cache.getBatch(TEST_KEYS);
        }
        
        long endTime = System.nanoTime();
        return (endTime - startTime) / 1_000_000; // 转换为毫秒
    }

    /**
     * 基准测试结果
     */
    public static class BenchmarkResult {
        public final long cacheTimeMs;
        public final long originalTimeMs;
        public final long batchTimeMs;
        public final int iterations;
        public final double cacheSpeedupRatio;
        public final double batchSpeedupRatio;

        BenchmarkResult(long cacheTimeMs, long originalTimeMs, long batchTimeMs, int iterations) {
            this.cacheTimeMs = cacheTimeMs;
            this.originalTimeMs = originalTimeMs;
            this.batchTimeMs = batchTimeMs;
            this.iterations = iterations;
            this.cacheSpeedupRatio = (double) originalTimeMs / cacheTimeMs;
            this.batchSpeedupRatio = (double) originalTimeMs / (batchTimeMs * 10); // 批量读取次数调整
        }

        @Override
        public String toString() {
            return String.format(
                "性能基准测试结果 (迭代次数: %d)\n" +
                "缓存读取耗时: %d ms\n" +
                "原始读取耗时: %d ms\n" +
                "批量读取耗时: %d ms (调整后)\n" +
                "缓存性能提升: %.2fx\n" +
                "批量读取性能提升: %.2fx\n" +
                "缓存效率提升: %.1f%%",
                iterations,
                cacheTimeMs,
                originalTimeMs,
                batchTimeMs * 10,
                cacheSpeedupRatio,
                batchSpeedupRatio,
                (cacheSpeedupRatio - 1) * 100
            );
        }

        /**
         * 获取简化的性能报告
         */
        public String getSimpleReport() {
            return String.format(
                "缓存性能: %dms vs 原始: %dms (提升%.1fx, 效率提升%.0f%%)",
                cacheTimeMs, originalTimeMs, cacheSpeedupRatio, (cacheSpeedupRatio - 1) * 100
            );
        }

        /**
         * 检查是否达到预期性能目标
         */
        public boolean meetsPerformanceTarget() {
            // 预期缓存至少比原始方式快3倍
            return cacheSpeedupRatio >= 3.0;
        }
    }

    /**
     * 运行快速性能测试
     */
    public static String runQuickBenchmark(Context context) {
        try {
            BenchmarkResult result = runBenchmark(context);
            return result.getSimpleReport();
        } catch (Exception e) {
            return "性能测试失败: " + e.getMessage();
        }
    }
}