# 反定位拉回功能实现任务清单

## 第一阶段：基础设施准备（高优先级）

1. **常量定义与配置**
   - 在`XConstant.java`中添加`ENABLE_LOCATION_LOOP_BROADCAST`常量用于功能总开关
   - 添加`ENABLE_SMART_LOCATION_LOOP_BROADCAST`常量用于智能反定位拉回开关
   - 添加`LOCATION_BROADCAST_INTERVAL`常量用于配置广播间隔（默认值设为2000ms）
   - 确保与现有常量风格保持一致

2. **基础结构添加**
   - 在`LocationPlugin`类中添加必要的成员变量：
     - `private Thread mLoopThread`：用于循环广播位置
     - `private volatile boolean mIsLoopRunning`：线程运行状态标记
     - `private static final Object LOOP_LOCK = new Object()`：线程同步锁
     - `private boolean mIsInForeground`：应用前台状态标记
     - `private long mLastSmartCheckTime`：上次智能模式检查时间
   - 添加位置缓存相关变量：
     - `private long mLastBroadcastTime`：上次广播时间记录
     - `private static final long MIN_BROADCAST_INTERVAL = 1000`：最小广播间隔
   - 添加与SmartLocationPlugin交互的方法：
     - `private SmartLocationPlugin getSmartLocationPlugin()`：获取智能定位插件实例

## 第二阶段：核心功能实现（高优先级）

3. **后台线程管理**
   - 实现`startLoopBroadcastLocation()`方法，创建并启动线程
   - 实现`stopLoopBroadcastLocation()`方法，安全停止线程
   - 确保线程安全和异常处理
   - 添加日志记录关键操作，便于调试

4. **位置广播实现**
   - 实现`broadcastLocation(LocationManager locationManager)`方法
   - 从现有位置获取模拟位置参数
   - 调用LocationManager的sendExtraCommand方法广播位置
   - 添加位置缓存机制，优化性能

5. **Hook方法扩展**
   - 修改`hook()`方法，根据配置启动循环广播
   - 在适当位置添加线程启动和停止调用
   - 确保与现有三阶段Hook策略兼容

6. **智能模式实现**
   - 实现`checkSmartLoopBroadcastMode()`方法，检查是否应该启用反定位拉回
   - 根据SmartLocationPlugin的状态决定是否启用反定位拉回
   - 添加前台/后台状态监听，在后台时自动关闭反定位拉回
   - 实现应用前台状态变化时的处理逻辑

7. **前台/后台状态监听**
   - 实现`registerActivityLifecycleCallbacks()`方法，监听应用生命周期
   - 在应用进入前台时，检查是否需要启动反定位拉回
   - 在应用进入后台时，停止反定位拉回以节省电量
   - 确保监听器的注册和注销正确处理

## 第三阶段：配置和持久化（中优先级）

8. **配置管理**
   - 创建`isLoopBroadcastEnabled()`方法检查功能是否启用
   - 创建`isSmartLoopBroadcastEnabled()`方法检查智能模式是否启用
   - 创建`shouldEnableLoopBroadcast()`方法综合判断是否应该启用反定位拉回
   - 实现SharedPreferences读写方法
   - 添加默认配置值和初始化逻辑

9. **插件生命周期管理**
   - 重写`onDestroy()`方法，确保在插件被销毁时停止线程
   - 添加应用前后台监听，优化电池使用
   - 确保在插件被销毁时注销所有监听器

## 第四阶段：UI界面实现（中优先级）

10. **设置界面扩展**
    - 找到`SettingsDialog`或相关UI类
    - 在打卡栏目内添加"反定位拉回"开关控件
    - 添加"智能反定位拉回"开关控件，并设置依赖关系
    - 添加配置说明和警告提示（如可能导致发热）
    - 实现设置变更事件监听

11. **开关依赖关系实现**
    - 实现"智能反定位拉回"开关只在"反定位拉回"开关打开时可见
    - 添加开关状态变化监听，实时更新UI状态
    - 实现开关状态变化时的功能启停逻辑

12. **用户交互优化**
    - 添加设置修改后的状态提示
    - 实现设置变更后的实时生效机制
    - 优化UI布局和文案
    - 添加功能说明提示，帮助用户理解两个开关的关系

## 第五阶段：优化和测试（低优先级）

13. **性能优化**
    - 优化广播频率和策略
    - 实现智能休眠机制，减少电池消耗
    - 添加线程优先级管理，减少系统资源占用
    - 优化与SmartLocationPlugin的交互，减少不必要的状态检查

14. **兼容性测试**
    - 在不同Android版本上测试功能
    - 测试与智能虚拟定位功能的兼容性
    - 验证在各种场景下的稳定性
    - 测试手动模式和智能模式的切换是否正常

15. **电池性能监控**
    - 添加电池消耗监控代码
    - 分析并优化电池使用情况
    - 考虑添加根据电池状态自动调整策略
    - 测试长时间运行对电池的影响

## 第六阶段：文档和说明（低优先级）

16. **功能文档更新**
    - 创建反定位拉回功能的说明文档
    - 更新README.md，添加新功能说明
    - 添加常见问题解答
    - 详细说明手动模式和智能模式的区别和使用场景

17. **代码注释完善**
    - 为关键方法添加详细注释
    - 说明线程安全保证机制
    - 完善异常处理说明
    - 说明与SmartLocationPlugin的交互机制

## 技术要点与注意事项

- **线程安全**：反定位拉回功能依赖后台线程，必须确保线程安全和正确终止
- **电池优化**：持续广播位置会消耗电池，需要平衡功效和电量消耗
- **异常处理**：全面的异常捕获和处理，确保不影响主应用
- **用户体验**：清晰明了的设置界面和用户提示
- **兼容性**：与现有虚拟定位和智能虚拟定位功能的兼容性
- **模式切换**：确保手动模式和智能模式之间的切换平滑无缝
- **状态同步**：确保与SmartLocationPlugin的状态同步准确及时
- **前后台处理**：正确处理应用前后台状态变化，避免不必要的资源消耗

## 关键代码参考

```java
// 后台线程启动方法参考
private synchronized void startLoopBroadcastLocation() {
    if (mIsLoopRunning || mLoopThread != null) return;
    
    synchronized (LOOP_LOCK) {
        if (mIsLoopRunning || mLoopThread != null) return;
        
        mIsLoopRunning = true;
        mLoopThread = new Thread(() -> {
            Alog.d(TAG, "位置广播线程启动");
            
            LocationManager locationManager = (LocationManager) 
                    getSystemContext().getSystemService(Context.LOCATION_SERVICE);
                    
            while (mIsLoopRunning) {
                try {
                    if (shouldEnableLoopBroadcast()) {
                        broadcastLocation(locationManager);
                    }
                    
                    Thread.sleep(getPInt(XConstant.Key.LOCATION_BROADCAST_INTERVAL, 2000));
                } catch (InterruptedException e) {
                    Alog.d(TAG, "位置广播线程中断");
                    break;
                } catch (Exception e) {
                    Alog.e(TAG, "位置广播出现异常: " + e.getMessage());
                }
            }
            
            Alog.d(TAG, "位置广播线程停止");
        });
        
        mLoopThread.setName("LocationBroadcast");
        mLoopThread.start();
    }
}

// 智能模式检查方法参考
private boolean checkSmartLoopBroadcastMode() {
    // 如果智能反定位拉回未启用，直接返回false
    if (!isEnable(XConstant.Key.ENABLE_SMART_LOCATION_LOOP_BROADCAST)) {
        return false;
    }
    
    // 如果应用在后台，不启用反定位拉回
    if (!mIsInForeground) {
        return false;
    }
    
    // 获取SmartLocationPlugin实例
    SmartLocationPlugin smartLocationPlugin = getSmartLocationPlugin();
    if (smartLocationPlugin == null) {
        return false;
    }
    
    // 检查智能虚拟定位是否启用以及当前是否应该启用虚拟定位
    boolean smartLocationEnabled = isEnable(XConstant.Key.ENABLE_SMART_LOCATION);
    if (!smartLocationEnabled) {
        return false;
    }
    
    // 如果智能虚拟定位启用了虚拟定位，则启用反定位拉回
    return smartLocationPlugin.checkAndUpdateLocationSetting();
}

// 综合判断是否应该启用反定位拉回
private boolean shouldEnableLoopBroadcast() {
    // 检查基本条件：虚拟定位和反定位拉回总开关都必须打开
    if (!isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION) || 
        !isEnable(XConstant.Key.ENABLE_LOCATION_LOOP_BROADCAST)) {
        return false;
    }
    
    // 检查是否启用智能模式
    if (isEnable(XConstant.Key.ENABLE_SMART_LOCATION_LOOP_BROADCAST)) {
        return checkSmartLoopBroadcastMode();
    }
    
    // 手动模式：只要总开关打开就启用
    return true;
}

// 前台/后台状态监听参考
private void registerActivityLifecycleCallbacks() {
    try {
        Application app = (Application) getSystemContext();
        app.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            private int activityCount = 0;
            
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {}
            
            @Override
            public void onActivityStarted(Activity activity) {
                if (activityCount == 0) {
                    // 应用进入前台
                    mIsInForeground = true;
                    Alog.d(TAG, "应用进入前台，检查是否需要启动反定位拉回");
                    checkAndUpdateLoopBroadcastState();
                }
                activityCount++;
            }
            
            @Override
            public void onActivityResumed(Activity activity) {}
            
            @Override
            public void onActivityPaused(Activity activity) {}
            
            @Override
            public void onActivityStopped(Activity activity) {
                activityCount--;
                if (activityCount == 0) {
                    // 应用进入后台
                    mIsInForeground = false;
                    Alog.d(TAG, "应用进入后台，停止反定位拉回");
                    stopLoopBroadcastLocation();
                }
            }
            
            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {}
            
            @Override
            public void onActivityDestroyed(Activity activity) {}
        });
    } catch (Exception e) {
        Alog.e(TAG, "注册活动生命周期回调失败", e);
    }
}

// 检查并更新反定位拉回状态
private void checkAndUpdateLoopBroadcastState() {
    if (shouldEnableLoopBroadcast()) {
        startLoopBroadcastLocation();
    } else {
        stopLoopBroadcastLocation();
    }
}
