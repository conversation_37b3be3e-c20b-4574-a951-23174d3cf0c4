/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.util;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import android.os.Process;
import android.text.TextUtils;

import com.sky.xposed.common.util.Alog;

import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 崩溃处理器，用于捕获和记录应用崩溃信息
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {

    private static final String TAG = "CrashHandler";
    private static final String CRASH_DIR = "crash";
    private static final String CRASH_LOG = "crash_";
    private static final String FILE_NAME_SUFFIX = ".log";

    private static CrashHandler sInstance = new CrashHandler();
    private Thread.UncaughtExceptionHandler mDefaultHandler;
    private Context mContext;
    private boolean mInitialized = false;

    private CrashHandler() {
    }

    public static CrashHandler getInstance() {
        return sInstance;
    }

    /**
     * 初始化崩溃处理器
     * @param context 应用上下文
     */
    public synchronized void initialize(Context context) {
        if (mInitialized) return;

        mContext = context.getApplicationContext();
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
        mInitialized = true;
        Alog.d(TAG, "崩溃处理器初始化完成");
    }

    @Override
    public void uncaughtException(Thread thread, Throwable throwable) {
        if (!handleException(throwable) && mDefaultHandler != null) {
            // 如果未处理，交给系统默认处理器处理
            mDefaultHandler.uncaughtException(thread, throwable);
        } else {
            // 结束进程
            Process.killProcess(Process.myPid());
            System.exit(1);
        }
    }

    /**
     * 处理崩溃异常
     * @param throwable 崩溃的异常信息
     * @return 是否已处理
     */
    private boolean handleException(Throwable throwable) {
        if (throwable == null) return false;

        try {
            // 保存崩溃日志
            saveCrashInfo(throwable);
            return true;
        } catch (Exception e) {
            Alog.e(TAG, "处理崩溃异常时出错", e);
            return false;
        }
    }

    /**
     * 保存崩溃信息到文件
     * @param throwable 崩溃异常
     */
    private void saveCrashInfo(Throwable throwable) {
        try {
            // 获取设备和应用信息
            StringBuilder sb = new StringBuilder();
            PackageManager pm = mContext.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(mContext.getPackageName(), PackageManager.GET_ACTIVITIES);
            
            // 添加时间信息
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            String time = format.format(new Date());
            sb.append("Time: ").append(time).append("\n\n");
            
            // 添加设备信息
            sb.append("Device Information:\n");
            sb.append("Brand: ").append(Build.BRAND).append("\n");
            sb.append("Model: ").append(Build.MODEL).append("\n");
            sb.append("Android Version: ").append(Build.VERSION.RELEASE).append("\n");
            sb.append("SDK: ").append(Build.VERSION.SDK_INT).append("\n\n");
            
            // 添加应用信息
            sb.append("App Information:\n");
            sb.append("Package: ").append(pi.packageName).append("\n");
            sb.append("Version: ").append(pi.versionName).append(" (").append(pi.versionCode).append(")\n\n");
            
            // 添加异常信息
            sb.append("Exception Information:\n");
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            throwable.printStackTrace(pw);
            sb.append(sw.toString());
            pw.close();
            
            // 保存文件
            String fileName = CRASH_LOG + time.replace(":", "-").replace(" ", "_") + FILE_NAME_SUFFIX;
            String crashDir = Environment.getExternalStorageDirectory().getPath() + 
                    File.separator + mContext.getPackageName() + File.separator + CRASH_DIR;
            
            File dir = new File(crashDir);
            if (!dir.exists()) dir.mkdirs();
            
            File file = new File(dir, fileName);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(sb.toString().getBytes());
            fos.close();
            
            Alog.d(TAG, "崩溃日志保存成功: " + file.getAbsolutePath());
        } catch (Exception e) {
            Alog.e(TAG, "保存崩溃日志失败", e);
        }
    }
} 