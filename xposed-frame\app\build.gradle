apply plugin: 'com.android.application'

android {
    compileSdkVersion 35
    defaultConfig {
        applicationId "com.sky.xposed.frame"
        minSdkVersion 26
        targetSdkVersion 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'com.github.sky-wei:xposed-javax:1.2.0'
    implementation 'com.github.sky-wei:xposed-common:1.2.0'
    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation project(':core')
    implementation project(':ui')
    implementation project(':annotations')
    compileOnly 'de.robv.android.xposed:api:82'
    compileOnly 'com.rover12421.AndroidHideApi:android:1.17'
    annotationProcessor project(":compiler")
    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
}
