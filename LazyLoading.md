# 插件延迟加载机制

## 概述

插件延迟加载机制是为了优化应用启动时间而设计的功能。通过将非关键插件的加载延迟到应用启动后进行，可以显著减少启动时间，提升用户体验。

## 架构设计

### 核心组件

1. **LazyLoadable接口** - 定义延迟加载插件的标准
2. **LazyPluginLoader类** - 管理延迟加载逻辑
3. **LazyBaseDingPlugin基类** - 支持延迟加载的插件基类
4. **PerformanceMonitor工具** - 性能监控和统计

### 加载优先级

```java
public enum Priority {
    CRITICAL(0),    // 关键插件，立即加载
    HIGH(1),        // 高优先级，延迟100ms加载
    NORMAL(2),      // 普通优先级，延迟500ms加载
    LOW(3);         // 低优先级，延迟1000ms加载
}
```

## 使用方法

### 1. 创建延迟加载插件

继承`LazyBaseDingPlugin`并实现相关方法：

```java
@APlugin
public class MyLazyPlugin extends LazyBaseDingPlugin {
    
    public MyLazyPlugin(XCoreManager coreManager) {
        super(coreManager);
    }
    
    @Override
    public Priority getLoadPriority() {
        return Priority.NORMAL; // 设置优先级
    }
    
    @Override
    public boolean canLazyLoad() {
        return true; // 允许延迟加载
    }
    
    @Override
    protected void doPreLazyLoad() {
        // 轻量级预处理
    }
    
    @Override
    protected void doLazyLoadComplete() {
        // 延迟加载完成后处理
    }
    
    @Override
    public void hook() {
        // 插件的主要Hook逻辑
    }
}
```

### 2. 配置插件优先级

在`PluginManager`中配置插件优先级：

```java
// 核心功能插件（最高优先级）
addPluginPriority("com.sky.xposed.rimet.plugin.LocationPlugin", 1);

// 重要但非核心插件
addPluginPriority("com.sky.xposed.rimet.plugin.SmartLocationPlugin", 2);

// 辅助功能插件
addPluginPriority("com.sky.xposed.rimet.plugin.WifiPlugin", 3);
```

## 工作流程

### 启动阶段

1. **应用启动** - 记录启动时间戳
2. **核心初始化** - 初始化核心组件
3. **插件分类** - 区分立即加载和延迟加载插件
4. **立即加载** - 加载关键插件
5. **延迟加载** - 按优先级延迟加载非关键插件

### 延迟加载流程

1. **添加到队列** - 延迟加载插件添加到LazyPluginLoader
2. **按优先级排序** - 根据Priority排序
3. **分批加载** - 按延迟时间分批执行
4. **状态跟踪** - 跟踪每个插件的加载状态
5. **完成回调** - 所有插件加载完成后的回调

## 性能监控

### 关键指标

- **核心初始化时间** - 从启动到核心组件初始化完成
- **立即加载时间** - 关键插件加载耗时
- **延迟加载时间** - 非关键插件加载耗时
- **总启动时间** - 完整启动流程耗时

### 监控方法

```java
PerformanceMonitor monitor = PerformanceMonitor.getInstance();

// 记录里程碑
monitor.recordMilestone("app_start", "应用启动");
monitor.recordMilestone("core_init_complete", "核心初始化完成");

// 生成报告
monitor.generateStartupReport();
```

## 预期效果

### 启动时间优化

- **目标**: 启动时间从10秒减少到7秒以内
- **改善**: 至少30%的启动时间减少
- **用户体验**: 更快的应用响应速度

### 资源使用优化

- **内存**: 延迟加载减少初始内存占用
- **CPU**: 分散CPU使用峰值
- **电池**: 减少启动时的电池消耗

## 最佳实践

### 插件分类原则

1. **关键插件** - 影响核心功能的插件应立即加载
2. **功能插件** - 独立功能模块可以延迟加载
3. **辅助插件** - 调试、统计等插件优先级最低

### 性能优化建议

1. **合理设置优先级** - 根据插件重要性设置合适的优先级
2. **轻量级预处理** - 在`doPreLazyLoad`中只做必要的初始化
3. **异常处理** - 确保延迟加载失败不影响主要功能
4. **监控和调优** - 定期检查性能数据并调整参数

## 故障排除

### 常见问题

1. **插件未延迟加载** - 检查是否正确继承LazyBaseDingPlugin
2. **加载顺序错误** - 检查优先级设置是否正确
3. **性能无改善** - 检查插件分类是否合理

### 调试方法

1. **查看日志** - 关注LazyPluginLoader的日志输出
2. **性能报告** - 使用PerformanceMonitor生成详细报告
3. **状态检查** - 使用getLazyLoadStatusDescription查看插件状态

## 示例代码

参考`LazyLoadTestPlugin`类了解完整的实现示例。

## 注意事项

1. **向后兼容** - 现有插件在不修改的情况下仍能正常工作
2. **线程安全** - 延迟加载在主线程执行，确保线程安全
3. **错误恢复** - 延迟加载失败时会回退到立即加载
4. **资源管理** - 注意插件的生命周期管理和资源释放
