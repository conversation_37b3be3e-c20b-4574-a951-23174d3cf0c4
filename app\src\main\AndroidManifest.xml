<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"
        android:minSdkVersion="26" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

    <application
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:name=".App"
        android:requestLegacyExternalStorage="true">
        <activity 
            android:name=".ui.activity.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="de.robv.android.xposed.category.MODULE_SETTINGS" />
            </intent-filter>
        </activity>

        <activity 
            android:name=".ui.activity.MapActivity"
            android:label="选择位置"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>

        <activity 
            android:name=".ui.activity.AnalysisActivity"
            android:label="自动适配"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="4be0cad1a3fa0de9f41dcefe4fcb0985"/>

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyCRM3fFGouJPAj2brfx6mFjYu6k7i_-N5g" />

        <service android:name="com.amap.api.location.APSService" />

        <meta-data
            android:name="xposedmodule"
            android:value="true"/>

        <meta-data
            android:name="xposeddescription"
            android:value="钉钉工具"/>

        <meta-data
            android:name="xposedminversion"
            android:value="54"/>
            
        <!-- LSPosed 推荐应用配置 -->
        <meta-data
            android:name="xposedscope"
            android:resource="@array/xposed_scope"/>
            
        <meta-data
            android:name="lsposed_recommended"
            android:value="com.alibaba.android.rimet"/>
    </application>

</manifest>