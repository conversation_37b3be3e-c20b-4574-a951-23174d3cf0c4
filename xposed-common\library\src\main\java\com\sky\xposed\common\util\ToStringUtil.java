/*
 * Copyright (c) 2018 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.common.util;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.Map;

/**
 * 对象信息输出工具类
 */
public class ToStringUtil {

    private ToStringUtil() {
    }

    /**
     * 将对象转为字符串表示
     * @param obj 对象
     * @return 字符串表示
     */
    public static String toString(Object obj) {
        if (obj == null) return "null";
        
        StringBuilder builder = new StringBuilder();
        builder.append(obj.getClass().getSimpleName()).append("{");
        
        try {
            Map<String, Object> values = getFieldValues(obj);
            boolean first = true;
            
            for (Map.Entry<String, Object> entry : values.entrySet()) {
                if (first) {
                    first = false;
                } else {
                    builder.append(", ");
                }
                builder.append(entry.getKey()).append("=").append(entry.getValue());
            }
        } catch (Exception e) {
            Alog.e("ToStringUtil", "转换对象异常", e);
            return obj.toString();
        }
        
        builder.append("}");
        return builder.toString();
    }
    
    /**
     * 将消息和对象转为字符串表示
     * @param message 消息文本
     * @param obj 对象
     * @return 字符串表示
     */
    public static String toString(String message, Object obj) {
        if (obj == null) return message + " null";
        
        StringBuilder builder = new StringBuilder();
        builder.append(message).append("\n");
        
        try {
            Map<String, Object> values = getFieldValues(obj);
            
            for (Map.Entry<String, Object> entry : values.entrySet()) {
                builder.append("  ").append(entry.getKey())
                       .append(" = ").append(entry.getValue()).append("\n");
            }
        } catch (Exception e) {
            Alog.e("ToStringUtil", "转换对象异常", e);
            builder.append(obj.toString());
        }
        
        return builder.toString();
    }
    
    /**
     * 获取字段值
     * @param obj 对象
     * @return 字段值Map
     */
    private static Map<String, Object> getFieldValues(Object obj) throws IllegalAccessException {
        Map<String, Object> values = new HashMap<>();
        Class<?> clazz = obj.getClass();
        
        while (clazz != null && clazz != Object.class) {
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                // 静态字段不处理
                if (Modifier.isStatic(field.getModifiers())) continue;
                
                field.setAccessible(true);
                values.put(field.getName(), field.get(obj));
            }
            
            clazz = clazz.getSuperclass();
        }
        
        return values;
    }
} 