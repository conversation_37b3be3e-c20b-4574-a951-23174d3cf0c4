/*
 * Copyright (c) 2020 The sky Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sky.xposed.rimet.plugin;

import android.location.Criteria;
import android.location.GpsStatus;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.Log;
import android.content.Context;
import android.app.Application;
import android.app.Activity;

import com.sky.xposed.annotations.APlugin;
import com.sky.xposed.common.util.Alog;
import com.sky.xposed.core.interfaces.XCoreManager;
import com.sky.xposed.rimet.XConstant;
import com.sky.xposed.rimet.plugin.base.BaseDingPlugin;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.Map;
import java.util.Random;
import java.util.WeakHashMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by sky on 2020-03-01.
 */
@APlugin()
public class LocationPlugin extends BaseDingPlugin {

    // 添加静态TAG常量
    private static final String TAG = "LocationPlugin";
    
    // 添加三阶段Hook的状态标记
    private volatile boolean mEssentialHookDone = false;
    private volatile boolean mSecondaryHookDone = false;
    private volatile boolean mOptionalHookDone = false;
    
    // 保留现有的状态变量
    private volatile boolean mFullHookExecuted = false;
    private volatile boolean mAMapHookExecuted = false;
    private volatile boolean mGPSLocationHookExecuted = false;
    
    // 添加方法缓存映射表
    private static final Map<String, Method> METHOD_CACHE = new ConcurrentHashMap<>();
    
    // 优化用于控制SmartLocationPlugin调用间隔
    private long mLastCheckTime = 0;
    private static final long CHECK_INTERVAL = 1000; // 1秒内不重复检查
    
    // 使用WeakHashMap缓存代理对象
    private final Map<Object, Object> mProxyCache = new WeakHashMap<>();
    
    // 使用静态Random对象
    private static final Random RANDOM = new Random();
    
    // 添加GPS状态参数缓存
    private static final int[] DEFAULT_PRNS = {3, 6, 9, 12, 15, 18, 21, 24};
    private static final int DEFAULT_EPHEMERIS_MASK = 0x00FFFFFF;
    private static final int DEFAULT_ALMANAC_MASK = 0x00FFFFFF;
    private static final int DEFAULT_USED_IN_FIX_MASK = 0x000000FF;
    
    // 添加位置缓存
    private Location mCachedLocation = null;
    private long mLastLocationUpdateTime = 0;
    private static final long LOCATION_CACHE_INTERVAL = 5000; // 5秒缓存一次位置

    // 反定位拉回功能所需的成员变量
    private Thread mLoopThread; // 用于循环广播位置
    private volatile boolean mIsLoopRunning; // 线程运行状态标记
    private static final Object LOOP_LOCK = new Object(); // 线程同步锁
    private boolean mIsInForeground = true; // 应用前台状态标记，默认为前台
    private long mLastSmartCheckTime = 0; // 上次智能模式检查时间
    private long mLastBroadcastTime = 0; // 上次广播时间记录
    private static final long MIN_BROADCAST_INTERVAL = 1000; // 最小广播间隔

    public LocationPlugin(XCoreManager coreManager) {
        super(coreManager);
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    public void hook() {
        Alog.d(TAG, "LocationPlugin 初始化...");
        
        // 记录开始时间
        final long startTime = System.currentTimeMillis();
        
        // 启用性能日志记录
        Alog.enablePerformanceLog(true);
        
        // 注册前台/后台状态监听
        registerActivityLifecycleCallbacks();
        
        // 第一阶段：必要的Hook
        Alog.beginTiming("LocationPlugin-Essential");
        setupEssentialHooks();
        Alog.endTiming(TAG, "LocationPlugin-Essential");
        
        // 第二阶段：次要Hook
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!mSecondaryHookDone) {
                Alog.beginTiming("LocationPlugin-Secondary");
                setupSecondaryHooks();
                mSecondaryHookDone = true;
                Alog.endTiming(TAG, "LocationPlugin-Secondary");
                
                Alog.d(TAG, "次要Hook加载完成, 耗时: " + (System.currentTimeMillis() - startTime) + "ms");
                
                // 检查并启动反定位拉回功能
                checkAndUpdateLoopBroadcastState();
            }
        }, 600);
        
        // 第三阶段：可选Hook
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            if (!mOptionalHookDone) {
                Alog.beginTiming("LocationPlugin-Optional");
                setupOptionalHooks();
                mOptionalHookDone = true;
                Alog.endTiming(TAG, "LocationPlugin-Optional");
                
                Alog.d(TAG, "完整Hook加载完成, 总耗时: " + (System.currentTimeMillis() - startTime) + "ms");
                
                // 输出线程信息
                Alog.thread(TAG, "Hook加载完成");
            }
        }, 1200);
    }
    
    /**
     * 检查并更新反定位拉回状态
     */
    private void checkAndUpdateLoopBroadcastState() {
        if (shouldEnableLoopBroadcast()) {
            startLoopBroadcastLocation();
        } else {
            stopLoopBroadcastLocation();
        }
    }
    
    /**
     * 注册应用前后台状态监听
     */
    private void registerActivityLifecycleCallbacks() {
        try {
            Application app = (Application) getLoadPackage().getContext();
            app.registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
                private int activityCount = 0;
                
                @Override
                public void onActivityCreated(Activity activity, Bundle savedInstanceState) {}
                
                @Override
                public void onActivityStarted(Activity activity) {
                    if (activityCount == 0) {
                        // 应用进入前台
                        mIsInForeground = true;
                        Alog.d(TAG, "应用进入前台，检查是否需要启动反定位拉回");
                        checkAndUpdateLoopBroadcastState();
                    }
                    activityCount++;
                }
                
                @Override
                public void onActivityResumed(Activity activity) {}
                
                @Override
                public void onActivityPaused(Activity activity) {}
                
                @Override
                public void onActivityStopped(Activity activity) {
                    activityCount--;
                    if (activityCount == 0) {
                        // 应用进入后台
                        mIsInForeground = false;
                        Alog.d(TAG, "应用进入后台，停止反定位拉回");
                        stopLoopBroadcastLocation();
                    }
                }
                
                @Override
                public void onActivitySaveInstanceState(Activity activity, Bundle outState) {}
                
                @Override
                public void onActivityDestroyed(Activity activity) {}
            });
        } catch (Exception e) {
            Alog.e(TAG, "注册活动生命周期回调失败", e);
        }
    }

    // 第一阶段：只加载最关键的Hook
    private void setupEssentialHooks() {
        // 只设置最小化的初始触发点
        setupInitialHook();
        
        // 标记基本Hook已完成
        mEssentialHookDone = true;
        Alog.d(TAG, "基本Hook加载完成");
    }
    
    // 第二阶段：加载次要的Hook
    private void setupSecondaryHooks() {
        // 设置GPS真正获取位置的初始Hook点
        setupGPSRealLocationHook();
        
        // 设置高德地图初始Hook点
        setupAMapInitialHook();
        
        Alog.d(TAG, "次要Hook点设置完成");
    }
    
    // 第三阶段：加载可选的完整Hook
    private void setupOptionalHooks() {
        // 设置完整的GPS状态和位置
        if (!mGPSLocationHookExecuted) {
            hookGPSProviderStatusComplete();
            hookGPSLocationComplete();
            mGPSLocationHookExecuted = true;
        }
        
        // 设置完整的高德地图
        if (!mAMapHookExecuted) {
            hookAMapComplete();
            mAMapHookExecuted = true;
        }
        
        Alog.d(TAG, "完整Hook设置完成");
    }

    // 设置GPS状态检查的初始Hook点
    private void setupInitialHook() {
        // 只监控GPS是否启用的检查，不执行任何实际Hook
        findMethod(
                LocationManager.class,
                "isProviderEnabled", String.class)
                .before(param -> {
                    if ("gps".equals(param.args[0])) {
                        Alog.d(TAG, "钉钉检查GPS是否启用");
                        
                        // 如果启用了虚拟定位，返回true
                        if (isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION)) {
                            param.setResult(true);
                        }
                        
                        // 触发智能定位检测
                        checkSmartLocationIfNeeded();
                    }
                });
    }
    
    // 设置监听真正获取GPS位置的方法
    private void setupGPSRealLocationHook() {
        // 监控getLastKnownLocation方法
        findMethod(
                LocationManager.class,
                "getLastKnownLocation", String.class)
                .before(param -> {
                    if ("gps".equals(param.args[0])) {
                        Alog.d(TAG, "钉钉尝试获取GPS最后位置");
                        performGPSLocationHook();
                    }
                });
                
        // 监控requestLocationUpdates方法，这是实际请求位置更新的核心方法
        findMethod(
                LocationManager.class,
                "requestLocationUpdates", String.class, long.class, float.class, LocationListener.class)
                .before(param -> {
                    if (param.args[0] != null && "gps".equals(param.args[0])) {
                        Alog.d(TAG, "钉钉请求GPS位置更新");
                        performGPSLocationHook();
                    }
                });
    }

    // 修改为空方法，不再执行任何实际Hook
    private synchronized void performFullHook() {
        // 避免重复执行
        if (mFullHookExecuted) return;
        
        Alog.d(TAG, "标记GPS基本Hook已执行");
        
        // 只设置标记，不执行任何实际Hook
        mFullHookExecuted = true;
    }
    
    // 执行GPS位置相关的完整Hook
    private synchronized void performGPSLocationHook() {
        // 避免重复执行
        if (mGPSLocationHookExecuted) return;
        
        Alog.d(TAG, "钉钉尝试获取真实GPS位置，开始执行GPS位置Hook...");
        
        // 确保基本Hook已标记
        performFullHook();
        
        // 执行GPS位置相关的完整Hook
        hookGPSProviderStatusComplete();
        hookGPSLocationComplete();
        
        // 标记GPS位置Hook已执行
        mGPSLocationHookExecuted = true;
        
        // 触发智能定位检测
        checkSmartLocationIfNeeded();
    }
    
    /**
     * GPS位置相关的Hook实现
     */
    private void hookGPSLocationComplete() {
        // Hook getLastKnownLocation方法
        findMethod(
                LocationManager.class,
                "getLastKnownLocation", String.class)
                .after(param -> {
                    if ("gps".equals(param.args[0]) && isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION)) {
                        Alog.d(TAG, "模拟GPS位置");
                        
                        // 创建或获取缓存的模拟位置
                        param.setResult(createMockLocation());
                    }
                });
        
        // Hook requestLocationUpdates方法
        findMethod(
                LocationManager.class,
                "requestLocationUpdates", String.class, long.class, float.class, LocationListener.class)
                .before(param -> {
                    if (param.args[0] != null && "gps".equals(param.args[0]) && param.args[3] != null && 
                            isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION)) {
                        Alog.d(TAG, "代理GPS位置监听器");
                        
                        // 代理位置监听器
                        param.args[3] = proxyLocationListener(param.args[3]);
                    }
                });
                
        // Hook getGpsStatus方法，用于模拟GPS状态
        findMethod(
                LocationManager.class,
                "getGpsStatus", GpsStatus.class)
                .after(param -> {
                    if (isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION)) {
                        GpsStatus gss = (GpsStatus) param.getResult();
                        if (gss != null) {
                            setGpsStatus(gss);
                            param.setResult(gss);
                        }
                    }
                });
    }
    
    /**
     * 完整的GPS状态Hook实现
     */
    private void hookGPSProviderStatusComplete() {
        // 重新设置完整的Hook
        findMethod(
                LocationManager.class,
                "isProviderEnabled", String.class)
                .before(param -> {
                    Alog.d(TAG, String.format("invoke isProviderEnabled arg0=%s", param.args[0]));
                    
                    // 钉钉尝试获取定位，触发智能虚拟定位检测
                    if ("gps".equals(param.args[0])) {
                        // 使用优化后的时间间隔检查
                        checkSmartLocationIfNeeded();
                    }
                    
                    if (isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION)) {
                        if ("gps".equals(param.args[0])) {
                            param.setResult(true);
                        }
                    }
                });
    }

    /**
     * 设置高德地图SDK的初始Hook点
     */
    private void setupAMapInitialHook() {
        // 监控高德地图SDK的getLastKnownLocation方法
        findMethod(
                "com.amap.api.location.AMapLocationClient",
                "getLastKnownLocation")
                .before(param -> {
                    // 钉钉尝试通过高德地图SDK获取位置，执行完整的高德地图Hook
                    performAMapHook();
                });
                
        // 监控高德地图SDK的setLocationListener方法
        findMethod(
                "com.amap.api.location.AMapLocationClient",
                "setLocationListener",
                "com.amap.api.location.AMapLocationListener")
                .before(param -> {
                    // 钉钉尝试通过高德地图SDK设置位置监听器，执行完整的高德地图Hook
                    performAMapHook();
                    
                    if (isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION)) {
                        // 执行基本的代理设置（确保初始Hook仍然有效）
                        param.args[0] = proxyLocationListener(param.args[0]);
                    }
                });
    }
    
    // 执行高德地图完整Hook操作
    private synchronized void performAMapHook() {
        // 避免重复执行
        if (mAMapHookExecuted) return;
        
        Alog.d(TAG, "钉钉尝试使用高德地图SDK获取位置，开始执行高德地图Hook...");
        
        // 确保基本Hook已标记（不再需要执行GPS完整Hook）
        performFullHook();
        
        // 执行完整的高德地图Hook
        hookAMapComplete();
        
        // 标记高德地图Hook已执行
        mAMapHookExecuted = true;
        
        // 触发智能定位检测
        checkSmartLocationIfNeeded();
    }
    
    /**
     * 完整的高德地图Hook实现
     */
    private void hookAMapComplete() {
        //hook amap getLastKnownLocation
        findMethod(
                "com.amap.api.location.AMapLocationClient",
                "getLastKnownLocation")
                .after(param -> {
                    Alog.d(TAG, "invoke amap getLastKnownLocation");
                    
                    // 使用优化后的时间间隔检查
                    checkSmartLocationIfNeeded();
                    
                    param.setResult(getLastKnownLocation(param.getResult()));
                });
        //hook amap setLocationListener
        findMethod(
                "com.amap.api.location.AMapLocationClient",
                "setLocationListener",
                "com.amap.api.location.AMapLocationListener")
                .before(param -> {
                    Alog.d(TAG, "invoke amap AMapLocationListener");
                    param.args[0] = proxyLocationListener(param.args[0]);
                });
    }
    
    // 缓存反射获取的方法
    private Method mGpsStatusMethod = null;
    
    private void setGpsStatus(GpsStatus gss) {
        try {
            Method m = findGpsStatusSetMethod();
            if (m == null) return;
            
            m.setAccessible(true);
            
            // 使用默认参数减少计算量
            int svCount = DEFAULT_PRNS.length;
            
            // 简化数组生成
            float[] snrs = new float[svCount];
            float[] elevations = new float[svCount];
            float[] azimuths = new float[svCount];
            
            // 批量生成参数，减少循环内计算量
            for (int i = 0; i < svCount; i++) {
                snrs[i] = 30 + RANDOM.nextInt(20); // 简化随机范围
                elevations[i] = 20 + RANDOM.nextInt(70); // 简化随机范围
                azimuths[i] = RANDOM.nextInt(360); // 简化为整数
            }
            
            m.invoke(gss, svCount, DEFAULT_PRNS, snrs, elevations, azimuths, 
                    DEFAULT_EPHEMERIS_MASK, DEFAULT_ALMANAC_MASK, DEFAULT_USED_IN_FIX_MASK);
            Alog.d(TAG, "设置GPS状态成功: 卫星数量=" + svCount);
        } catch (Exception e) {
            Alog.e(TAG, "设置GPS状态失败: " + e.getMessage());
        }
    }
    
    private Method findGpsStatusSetMethod() {
        if (mGpsStatusMethod != null) return mGpsStatusMethod;
        
        // 先从缓存中查找
        String key = GpsStatus.class.getName() + "#setStatus";
        Method cachedMethod = METHOD_CACHE.get(key);
        if (cachedMethod != null) return cachedMethod;
        
        try {
            Class<?> clazz = GpsStatus.class;
            for (Method method : clazz.getDeclaredMethods()) {
                if (method.getName().equals("setStatus") && method.getParameterTypes().length > 1) {
                    method.setAccessible(true);
                    mGpsStatusMethod = method;
                    
                    // 添加到缓存
                    METHOD_CACHE.put(key, method);
                    return method;
                }
            }
        } catch (Exception e) {
            Alog.e(TAG, "获取GPS状态方法失败");
        }
        return null;
    }
    
    /**
     * 创建模拟位置（使用缓存机制）
     */
    private Location createMockLocation() {
        long now = System.currentTimeMillis();
        
        // 使用缓存减少对象创建
        if (mCachedLocation != null && now - mLastLocationUpdateTime < LOCATION_CACHE_INTERVAL) {
            // 只更新时间戳
            mCachedLocation.setTime(now);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                mCachedLocation.setElapsedRealtimeNanos(System.nanoTime());
            }
            Alog.d(TAG, "使用缓存的位置数据");
            return mCachedLocation;
        }
        
        Alog.beginTiming("createMockLocation");
        
        // 创建新的虚拟位置
        Location l = new Location(LocationManager.GPS_PROVIDER);
        
        String latitude = getPString(XConstant.Key.LOCATION_LATITUDE);
        String longitude = getPString(XConstant.Key.LOCATION_LONGITUDE);
        
        if (!TextUtils.isEmpty(latitude) && !TextUtils.isEmpty(longitude)) {
            double latValue = Double.parseDouble(latitude);
            double lngValue = Double.parseDouble(longitude);
            
            // 预计算随机偏移
            double offset = (RANDOM.nextInt(13) + 3) / 100000.0;
            
            l.setLatitude(latValue + offset);
            l.setLongitude(lngValue + offset);
            l.setAccuracy(10.0f);
            l.setTime(now);
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                l.setElapsedRealtimeNanos(System.nanoTime());
            }
            
            // 更新缓存
            mCachedLocation = l;
            mLastLocationUpdateTime = now;
        }
        
        Alog.endTiming(TAG, "createMockLocation");
        return l;
    }

    /**
     * 获取最后一次位置信息
     */
    private Object getLastKnownLocation(Object location) {
        return isEnableVirtualLocation() ? null : location;
    }

    /**
     * 代理位置监听
     */
    private Object proxyLocationListener(Object listener) {
        if (listener == null || Proxy.isProxyClass(listener.getClass())) {
            return listener;
        }
        
        // 检查缓存
        if (mProxyCache.containsKey(listener)) {
            return mProxyCache.get(listener);
        }
        
        // 创建新代理并缓存
        Object proxy = Proxy.newProxyInstance(
                listener.getClass().getClassLoader(),
                listener.getClass().getInterfaces(),
                new AMapLocationListenerProxy(listener));
        mProxyCache.put(listener, proxy);
        return proxy;
    }

    /**
     * 是否启用虚拟位置
     */
    private boolean isEnableVirtualLocation() {
        return isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION);
    }

    /**
     * 位置监听代理
     */
    private final class AMapLocationListenerProxy implements InvocationHandler {

        private Object mListener;

        private AMapLocationListenerProxy(Object listener) {
            mListener = listener;
        }

        @Override
        public Object invoke(Object o, Method method, Object[] objects) throws Throwable {
            if ("onLocationChanged".equals(method.getName())) {
                // 使用优化后的检查方法
                checkSmartLocationIfNeeded();
                
                // 如果虚拟定位开启，处理位置数据
                if (isEnableVirtualLocation()) {
                    // 开始处理
                    handlerLocationChanged(objects);
                }
            }
            return method.invoke(mListener, objects);
        }

        private void handlerLocationChanged(Object[] objects) {
            if (objects == null || objects.length != 1) return;

            Location location = (Location) objects[0];

            String latitude = getPString(XConstant.Key.LOCATION_LATITUDE);
            String longitude = getPString(XConstant.Key.LOCATION_LONGITUDE);

            if (!TextUtils.isEmpty(latitude) && !TextUtils.isEmpty(longitude)) {
                // 减少随机数范围，优化计算
                double latValue = Double.parseDouble(latitude);
                double lngValue = Double.parseDouble(longitude);
                
                // 预计算随机偏移量
                double offset = (RANDOM.nextInt(13) + 3) / 100000.0;
                
                location.setLatitude(latValue + offset);
                location.setLongitude(lngValue + offset);
            }
        }
    }

    // 优化智能定位检查的方法
    private void checkSmartLocationIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - mLastCheckTime > CHECK_INTERVAL) {
            mLastCheckTime = currentTime;
            SmartLocationPlugin smartPlugin = getSmartLocationPlugin();
            if (smartPlugin != null) {
                Alog.beginTiming("smartLocationCheck");
                boolean result = smartPlugin.checkAndUpdateLocationSetting();
                Alog.endTiming(TAG, "smartLocationCheck");
                
                // 使用条件日志输出结果
                Alog.dIf(result, TAG, "智能定位检查结果: 已启用虚拟定位");
            }
        }
    }
    
    /**
     * 获取SmartLocationPlugin实例
     * @return SmartLocationPlugin实例，如果不存在则返回null
     */
    private SmartLocationPlugin getSmartLocationPlugin() {
        return SmartLocationPlugin.getInstance();
    }

    /**
     * 优化的反射方法查找，使用缓存
     */
    private Method findReflectionMethod(String className, String methodName) {
        String key = className + "#" + methodName;
        
        // 先查找缓存
        Method cachedMethod = METHOD_CACHE.get(key);
        if (cachedMethod != null) return cachedMethod;
        
        // 缓存未命中，使用反射查找
        try {
            Class<?> clazz = Class.forName(className);
            for (Method method : clazz.getDeclaredMethods()) {
                if (method.getName().equals(methodName)) {
                    method.setAccessible(true);
                    
                    // 添加到缓存
                    METHOD_CACHE.put(key, method);
                    return method;
                }
            }
        } catch (Exception e) {
            Alog.e(TAG, "获取方法失败: " + className + "#" + methodName, e);
        }
        return null;
    }

    /**
     * 开始循环广播位置
     * 创建并启动后台线程，定期广播位置信息
     */
    private synchronized void startLoopBroadcastLocation() {
        if (mIsLoopRunning || mLoopThread != null) return;
        
        synchronized (LOOP_LOCK) {
            if (mIsLoopRunning || mLoopThread != null) return;
            
            mIsLoopRunning = true;
            mLoopThread = new Thread(() -> {
                Alog.d(TAG, "位置广播线程启动");
                
                LocationManager locationManager = (LocationManager) 
                        getLoadPackage().getContext().getSystemService(Context.LOCATION_SERVICE);
                        
                while (mIsLoopRunning) {
                    try {
                        if (shouldEnableLoopBroadcast()) {
                            broadcastLocation(locationManager);
                        }
                        
                        // 获取广播间隔，确保不小于最小间隔
                        long interval = getPInt(XConstant.Key.LOCATION_BROADCAST_INTERVAL, 2000);
                        if (interval < MIN_BROADCAST_INTERVAL) {
                            interval = MIN_BROADCAST_INTERVAL;
                            Alog.d(TAG, "广播间隔过小，已调整为最小值: " + MIN_BROADCAST_INTERVAL + "ms");
                        }
                        
                        Thread.sleep(interval);
                    } catch (InterruptedException e) {
                        Alog.d(TAG, "位置广播线程中断");
                        break;
                    } catch (Exception e) {
                        Alog.e(TAG, "位置广播出现异常: " + e.getMessage());
                    }
                }
                
                Alog.d(TAG, "位置广播线程停止");
            });
            
            mLoopThread.setName("LocationBroadcast");
            mLoopThread.start();
        }
    }

    /**
     * 停止循环广播位置
     * 安全地停止后台线程
     */
    private synchronized void stopLoopBroadcastLocation() {
        if (!mIsLoopRunning || mLoopThread == null) return;
        
        synchronized (LOOP_LOCK) {
            if (!mIsLoopRunning || mLoopThread == null) return;
            
            mIsLoopRunning = false;
            try {
                mLoopThread.interrupt();
                mLoopThread = null;
                Alog.d(TAG, "位置广播线程已停止");
            } catch (Exception e) {
                Alog.e(TAG, "停止位置广播线程出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 广播位置信息
     * @param locationManager 系统LocationManager对象
     */
    private void broadcastLocation(LocationManager locationManager) {
        try {
            long currentTime = System.currentTimeMillis();
            
            // 控制广播频率，避免过于频繁
            if (currentTime - mLastBroadcastTime < MIN_BROADCAST_INTERVAL) {
                return;
            }
            
            // 创建或获取缓存的模拟位置
            Location location = mCachedLocation;
            if (location == null || (currentTime - mLastLocationUpdateTime) > LOCATION_CACHE_INTERVAL) {
                location = createMockLocation();
                mCachedLocation = location;
                mLastLocationUpdateTime = currentTime;
            }
            
            if (location != null) {
                // 使用sendExtraCommand广播位置
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // Android 11及以上使用新方法
                    Bundle bundle = new Bundle();
                    bundle.putParcelable("location", location);
                    locationManager.sendExtraCommand(
                            LocationManager.GPS_PROVIDER,
                            "inject_location",
                            bundle);
                } else {
                    // Android 10及以下使用旧方法
                    Bundle bundle = new Bundle();
                    bundle.putParcelable("location", location);
                    locationManager.sendExtraCommand(
                            LocationManager.GPS_PROVIDER,
                            "inject_location",
                            bundle);
                }
                
                mLastBroadcastTime = currentTime;
                Alog.d(TAG, "位置广播成功: " + location.getLatitude() + ", " + location.getLongitude());
            }
        } catch (Exception e) {
            Alog.e(TAG, "广播位置时出错: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否应该启用循环广播
     * @return true如果应该启用循环广播
     */
    private boolean shouldEnableLoopBroadcast() {
        // 检查基本条件：虚拟定位和反定位拉回总开关都必须打开
        if (!isEnable(XConstant.Key.ENABLE_VIRTUAL_LOCATION) || 
            !isLoopBroadcastEnabled()) {
            return false;
        }
        
        // 检查是否启用智能模式
        if (isSmartLoopBroadcastEnabled()) {
            return checkSmartLoopBroadcastMode();
        }
        
        // 手动模式：只要总开关打开就启用
        return true;
    }
    
    /**
     * 检查反定位拉回功能是否启用
     * @return true如果反定位拉回功能启用
     */
    private boolean isLoopBroadcastEnabled() {
        return isEnable(XConstant.Key.ENABLE_LOCATION_LOOP_BROADCAST);
    }
    
    /**
     * 检查智能反定位拉回模式是否启用
     * @return true如果智能反定位拉回模式启用
     */
    private boolean isSmartLoopBroadcastEnabled() {
        return isEnable(XConstant.Key.ENABLE_SMART_LOCATION_LOOP_BROADCAST);
    }
    
    /**
     * 智能模式检查
     * @return true如果智能模式应该启用循环广播
     */
    private boolean checkSmartLoopBroadcastMode() {
        // 控制检查频率
        long currentTime = System.currentTimeMillis();
        if (currentTime - mLastSmartCheckTime < 1000) { // 1秒内不重复检查
            return false;
        }
        mLastSmartCheckTime = currentTime;
        
        // 如果智能反定位拉回未启用，直接返回false
        if (!isSmartLoopBroadcastEnabled()) {
            return false;
        }
        
        // 如果应用在后台，不启用反定位拉回
        if (!mIsInForeground) {
            return false;
        }
        
        // 获取SmartLocationPlugin实例
        SmartLocationPlugin smartLocationPlugin = getSmartLocationPlugin();
        if (smartLocationPlugin == null) {
            return false;
        }
        
        // 检查智能虚拟定位是否启用以及当前是否应该启用虚拟定位
        boolean smartLocationEnabled = isEnable(XConstant.Key.ENABLE_SMART_LOCATION);
        if (!smartLocationEnabled) {
            return false;
        }
        
        // 如果智能虚拟定位启用了虚拟定位，则启用反定位拉回
        return smartLocationPlugin.checkAndUpdateLocationSetting();
    }

    /**
     * 插件销毁时释放资源
     */
    @Override
    public void release() {
        // 停止位置广播线程
        stopLoopBroadcastLocation();
        
        // 在此调用父类方法，确保执行原有的释放逻辑
        super.release();
    }
}
